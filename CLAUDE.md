# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is "K<PERSON>小红书工具箱应用" (KOL Xiaohongshu Toolbox Application), an Electron-based desktop application for analyzing and monitoring Xiaohongshu (Little Red Book) content. The application provides comprehensive features including note analysis, blogger monitoring, batch processing, media tools, and data export functionality.

## Development Commands

### Development
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build application (TypeScript compilation + Vite build)
- `npm run preview` - Preview built application

### Building for Distribution
- `npm run build:mac` - Build macOS application
- `npm run build:mac-universal` - Build universal macOS app (Intel + Apple Silicon)
- `npm run build:win` - Build Windows application
- `npm run build:all` - Build for all platforms simultaneously

### Build Notes
- Always run `npm run build` before building distribution versions
- Distribution configuration is in `electron-builder.yml`
- Built electron code outputs to `dist-electron/`
- Vite build outputs to `dist/`

## Architecture

### Technology Stack
- **Frontend**: Vue 3 + TypeScript + Vite
- **UI Framework**: Element Plus
- **State Management**: Pinia (with persistence via electron-store)
- **Desktop Framework**: Electron
- **Build Tool**: Vite with electron plugin
- **Media Processing**: FFmpeg (via fluent-ffmpeg), Sharp
- **Speech Recognition**: BcutASR
- **Data Processing**: node-xlsx, axios
- **Task Scheduling**: node-schedule

### Core Directory Structure
```
electron/main/          # Electron main process
├── api/               # Xiaohongshu API integration and web scraping
├── ipc/               # IPC handlers (modularized by domain)
├── services/          # Backend services (monitoring, workers, media)
├── store/             # Electron-store persistence layer
└── utils/             # Utility functions

src/                   # Vue frontend (renderer process)
├── components/        # Vue components
├── views/             # Page-level components
├── stores/            # Pinia stores
└── router/            # Vue Router configuration
```

### IPC Architecture
The application uses a modular IPC system with handlers organized by domain:
- `ipc/xhs.ts` - Xiaohongshu API and scraping operations
- `ipc/monitor.ts` - Monitoring task management (10-second delay on startup)
- `ipc/excel.ts` - Excel import/export functionality
- `ipc/app.ts` - Application-level operations (login, logout, etc.)
- `ipc/media.ts` - Media processing operations
- `ipc/fileSystem.ts` - File system operations
- `ipc/updater.ts` - Auto-update functionality

All IPC handlers follow a consistent response format:
```typescript
{
  success: boolean,
  data?: any,
  error?: string
}
```

### Core Services
- **XhsService** (`services/xhs-worker.ts`) - Main Xiaohongshu data fetching service
- **MonitorService** (`services/monitor-service.ts`) - Task scheduling and monitoring
- **ExcelService** (`services/excel-worker.ts`) - Excel processing worker
- **MediaService** (`services/media-service.ts`) - Coordinates all media operations
- **FFmpegService** (`services/ffmpeg-service.ts`) - FFmpeg operations wrapper
- **ASRService** (`services/asr-service.ts`) - Speech recognition service

### API Integration
- **WebXhsAPI** (`api/web-xhs-api.ts`) - Main web-based Xiaohongshu API client
- **XhsSpider** (`api/xhs-spider.ts`) - Web scraping functionality
- Uses hidden browser windows for authentication and data fetching

### Window Management
The application manages multiple windows:
- Main window - Primary UI
- Login window - Hidden Xiaohongshu authentication window (can be toggled)
- Window states managed in `windows.ts` and `appState.ts`

### Data Models
Key TypeScript interfaces in `api/models/`:
- `Note` - Xiaohongshu note/post data structure
- `WebNote` - Web-specific note format
- `User` - User profile information

## Feature Routes

- `/` (Home) - Single note analysis and media download
- `/note` - Batch note fetching by keywords
- `/batch` - Batch processing from Excel files
- `/monitor` - Real-time monitoring dashboard
  - Note monitoring tasks (track engagement metrics)
  - Blogger monitoring tasks (track new posts)
- `/media` - Media processing tools
  - Video conversion
  - Audio extraction
  - Speech recognition (ASR)
  - Image processing
- `/check` and `/user_check` - Data validation tools

## Media Processing Commands

### FFmpeg Integration
The application uses bundled FFmpeg binaries via `@ffmpeg-installer/ffmpeg`. Supported operations include:

```typescript
// Video conversion
window.electronAPI.media.convertVideo(inputPath, outputPath, {
  format: 'mp4',  // mp4, avi, mov, mkv, flv, wmv
  quality: 'high', // low, medium, high, highest
  size: 'original' // original, 720p, 480p, custom
}, taskId)

// Audio extraction
window.electronAPI.media.extractAudio(videoPath, audioPath, {
  format: 'mp3',   // mp3, wav, aac, flac, ogg
  quality: '192k'  // 64k, 128k, 192k, 256k, 320k
}, taskId)

// Speech recognition
window.electronAPI.media.extractText(audioPath, {
  model: 'small',  // small, medium, large
  removeFiller: true
}, taskId)

// Image processing
window.electronAPI.media.processImages(imagePaths, {
  format: 'jpeg',  // jpeg, png, webp
  quality: 80,     // 1-100
  resize: { width: 1920, height: 1080 }
}, taskId)
```

### Task Management
```typescript
// Task control
window.electronAPI.media.pauseTask(taskId)
window.electronAPI.media.resumeTask(taskId)
window.electronAPI.media.cancelTask(taskId)

// Progress monitoring
const unsubscribe = window.electronAPI.media.onTaskProgress((data) => {
  console.log(`Task ${data.taskId}: ${data.progress}%`)
})
```

## State Management

### Pinia Stores
- **Media Module**: `media-main`, `media-tasks`, `media-settings`, `media-stats`
- **Monitor Module**: Uses electron-store for persistence
- All stores support persistence through custom plugin

### Event-Driven Updates
Media processing uses internal event bus for real-time updates:
```typescript
mediaEventBus.on('task:progress-changed', ({ taskId, progress }) => {
  // Handle progress update
})
```

## Development Guidelines

### Authentication Flow
The application uses a hidden login window to open Xiaohongshu web interface for cookie-based authentication. Cookies are managed through `electron-store` and `tough-cookie`.

### Monitor System
Background monitoring uses `node-schedule` for task scheduling. Monitoring tasks are persisted in electron-store and can track individual notes and blogger profiles.

### File Processing
- Excel operations use `node-xlsx`
- Image processing uses `sharp`
- All file operations pass through IPC to main process

### Error Handling
Most async operations return consistent format for error handling between main and renderer processes:
```typescript
{
  success: boolean,
  data?: any,
  error?: string
}
```

### File Size Constraints
- **Video files**: Maximum 1GB
- **Audio files for ASR**: Maximum 100MB
- **Batch operations**: Designed for handling large datasets efficiently

### Performance Considerations
- Media processing runs in separate workers to avoid blocking
- Monitor tasks use 10-second startup delay to prevent initialization issues
- Large file operations provide progress updates via IPC events