import ffmpeg from 'fluent-ffmpeg';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';

export interface ConvertOptions {
  outputFormat: string;
  quality?: string;
  maxWidth?: number;
  resizeEnabled?: boolean;
}

export interface MediaInfo {
  duration?: number;
  dimensions?: { width: number; height: number };
  bitrate?: number;
  codec?: string;
}

export class FFmpegService extends EventEmitter {
  private activeCommands: Map<string, ffmpeg.FfmpegCommand> = new Map();
  private progressCallbacks: Map<string, (progress: any) => void> = new Map();
  private ffmpegPath: string = '';

  constructor() {
    super();
    this.initFFmpegPath();
  }

  /**
   * 初始化FFmpeg路径
   */
  private async initFFmpegPath(): Promise<void> {
    try {
      // 尝试动态导入 @ffmpeg-installer/ffmpeg
      const ffmpegInstaller = await import('@ffmpeg-installer/ffmpeg');
      this.ffmpegPath = ffmpegInstaller.default.path || ffmpegInstaller.path;
      ffmpeg.setFfmpegPath(this.ffmpegPath);
      console.log(`[FFmpegService] FFmpeg service initialized with path: ${this.ffmpegPath}`);
    } catch (error) {
      console.error(`[FFmpegService] Failed to load FFmpeg installer:`, error);
      // 尝试使用系统FFmpeg
      this.ffmpegPath = 'ffmpeg';
      ffmpeg.setFfmpegPath(this.ffmpegPath);
      console.log(`[FFmpegService] Using system FFmpeg: ${this.ffmpegPath}`);
    }
  }

  /**
   * 视频格式转换
   */
  async convertVideo(inputPath: string, outputPath: string, options: ConvertOptions, taskId?: string): Promise<string> {
    console.log(`[FFmpegService] 开始视频转换: ${inputPath} -> ${outputPath}`);

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 先获取视频信息，包括总时长
    const mediaInfo = await this.getMediaInfo(inputPath);
    const totalDuration = mediaInfo.duration || 0;
    console.log(`[FFmpegService] 视频总时长: ${totalDuration}秒`);

    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath);

      // 设置输出格式
      command = command.format(options.outputFormat);

      // 设置质量参数
      if (options.quality) {
        // 解析质量参数，如 "192k" -> 192
        const qualityValue = options.quality.replace('k', '');
        command = command.videoBitrate(qualityValue);
      }

      // 设置尺寸调整
      if (options.resizeEnabled && options.maxWidth) {
        command = command.size(`${options.maxWidth}x?`);
      }

      // 如果有taskId，存储命令用于后续控制
      if (taskId) {
        this.activeCommands.set(taskId, command);
      }

      // 进度监听
      command.on('progress', (progress) => {
        // 使用当前处理时间与视频总时长比较计算百分比
        const currentTime = progress.timemark ? this.parseTimestamp(progress.timemark) : 0;
        const percent = totalDuration > 0 ? Math.round((currentTime / totalDuration) * 100) : 0;

        this.emitProgress(taskId, {
          percent: Math.min(percent, 100),
          fps: progress.frames,
          speed: progress.currentFps,
          size: progress.targetSize
        });
      });

      // 错误处理
      command.on('error', (err) => {
        console.error(`[FFmpegService] 视频转换失败:`, err);
        if (taskId) {
          this.activeCommands.delete(taskId);
        }
        reject(new Error(`视频转换失败: ${err.message}`));
      });

      // 完成处理
      command.on('end', () => {
        console.log(`[FFmpegService] 视频转换完成: ${outputPath}`);
        if (taskId) {
          this.activeCommands.delete(taskId);
        }
        resolve(outputPath);
      });

      // 开始转换
      command.save(outputPath);
    });
  }

  /**
   * 从视频提取音频
   */
  async extractAudio(videoPath: string, audioPath: string, options?: { quality?: string }, taskId?: string): Promise<string> {
    console.log(`[FFmpegService] 开始音频提取: ${videoPath} -> ${audioPath}`);

    // 确保输出目录存在
    const outputDir = path.dirname(audioPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 先获取视频信息，包括总时长
    const mediaInfo = await this.getMediaInfo(videoPath);
    const totalDuration = mediaInfo.duration || 0;
    console.log(`[FFmpegService] 视频总时长: ${totalDuration}秒`);

    return new Promise((resolve, reject) => {
      let command = ffmpeg(videoPath);

      // 只提取音频
      command = command.noVideo();

      // 根据输出格式设置相应的编码器和参数
      const ext = path.extname(audioPath).slice(1).toLowerCase();

      switch (ext) {
        case 'wav':
          // WAV格式使用PCM编码器，无损格式不需要设置比特率
          command = command
            .format('wav')
            .audioCodec('pcm_s16le')  // 明确指定PCM编码器
            .audioFrequency(44100)    // 设置采样率
            .audioChannels(2);        // 设置声道数
          console.log(`[FFmpegService] 配置WAV格式: PCM编码器, 44.1kHz, 立体声`);
          break;

        case 'mp3':
          command = command
            .format('mp3')
            .audioCodec('libmp3lame');
          if (options?.quality) {
            const qualityValue = this.parseQualityValue(options.quality);
            command = command.audioBitrate(qualityValue);
          } else {
            command = command.audioBitrate('192k'); // 默认比特率
          }
          console.log(`[FFmpegService] 配置MP3格式: libmp3lame编码器`);
          break;

        case 'aac':
          command = command
            .format('aac')
            .audioCodec('aac');
          if (options?.quality) {
            const qualityValue = this.parseQualityValue(options.quality);
            command = command.audioBitrate(qualityValue);
          } else {
            command = command.audioBitrate('128k'); // 默认比特率
          }
          console.log(`[FFmpegService] 配置AAC格式: aac编码器`);
          break;

        case 'flac':
          // FLAC是无损格式，不需要设置比特率
          command = command
            .format('flac')
            .audioCodec('flac');
          console.log(`[FFmpegService] 配置FLAC格式: flac编码器`);
          break;

        case 'ogg':
          command = command
            .format('ogg')
            .audioCodec('libvorbis');
          if (options?.quality) {
            const qualityValue = this.parseQualityValue(options.quality);
            command = command.audioBitrate(qualityValue);
          } else {
            command = command.audioBitrate('192k'); // 默认比特率
          }
          console.log(`[FFmpegService] 配置OGG格式: libvorbis编码器`);
          break;

        default:
          // 默认处理
          command = command.format(ext);
          if (options?.quality) {
            const qualityValue = this.parseQualityValue(options.quality);
            command = command.audioBitrate(qualityValue);
          }
          console.log(`[FFmpegService] 配置${ext.toUpperCase()}格式: 默认编码器`);
          break;
      }

      // 如果有taskId，存储命令
      if (taskId) {
        this.activeCommands.set(taskId, command);
      }

      // 进度和错误处理
      command.on('progress', (progress) => {
        // 使用当前处理时间与视频总时长比较计算百分比
        const currentTime = progress.timemark ? this.parseTimestamp(progress.timemark) : 0;
        const percent = totalDuration > 0 ? Math.round((currentTime / totalDuration) * 100) : 0;

        this.emitProgress(taskId, {
          percent: Math.min(percent, 100),
          size: progress.targetSize
        });
      });

      command.on('error', (err) => {
        console.error(`[FFmpegService] 音频提取失败:`, err);
        if (taskId) {
          this.activeCommands.delete(taskId);
        }
        reject(new Error(`音频提取失败: ${err.message}`));
      });

      command.on('end', () => {
        console.log(`[FFmpegService] 音频提取完成: ${audioPath}`);
        if (taskId) {
          this.activeCommands.delete(taskId);
        }
        resolve(audioPath);
      });

      command.save(audioPath);
    });
  }

  /**
   * 获取媒体文件信息
   */
  async getMediaInfo(filePath: string): Promise<MediaInfo> {
    console.log(`[FFmpegService] 获取媒体信息: ${filePath}`);

    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          console.error(`[FFmpegService] 获取媒体信息失败:`, err);
          reject(new Error(`获取媒体信息失败: ${err.message}`));
          return;
        }

        const videoStream = metadata.streams.find(s => s.codec_type === 'video');
        const audioStream = metadata.streams.find(s => s.codec_type === 'audio');

        const mediaInfo: MediaInfo = {
          duration: metadata.format.duration,
          bitrate: metadata.format.bit_rate ? parseInt(metadata.format.bit_rate.toString()) : undefined
        };

        if (videoStream) {
          mediaInfo.dimensions = {
            width: videoStream.width || 0,
            height: videoStream.height || 0
          };
          mediaInfo.codec = videoStream.codec_name;
        } else if (audioStream) {
          mediaInfo.codec = audioStream.codec_name;
        }

        resolve(mediaInfo);
      });
    });
  }

  /**
   * 从视频中提取帧作为图片
   */
  async extractFrame(videoPath: string, outputPath: string, timeOffset: number = 5): Promise<string> {
    console.log(`[FFmpegService] 模拟提取视频帧: ${videoPath} -> ${outputPath}`);

    // TODO: 实现真实的FFmpeg帧提取
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟处理时间

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 模拟创建图片文件
    fs.writeFileSync(outputPath, Buffer.alloc(1024));

    return outputPath;
  }

  /**
   * 合并音频和视频
   */
  async mergeAudioVideo(videoPath: string, audioPath: string, outputPath: string): Promise<string> {
    console.log(`[FFmpegService] 模拟合并音视频: ${videoPath} + ${audioPath} -> ${outputPath}`);

    // TODO: 实现真实的FFmpeg合并
    await new Promise(resolve => setTimeout(resolve, 1500)); // 模拟处理时间

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 模拟创建合并文件
    if (fs.existsSync(videoPath)) {
      fs.copyFileSync(videoPath, outputPath);
    }

    return outputPath;
  }

  /**
   * 调整视频速度
   */
  async changeVideoSpeed(inputPath: string, outputPath: string, speed: number): Promise<string> {
    console.log(`[FFmpegService] 模拟调整视频速度: ${inputPath} -> ${outputPath}, 速度: ${speed}x`);

    // TODO: 实现真实的FFmpeg速度调整
    await new Promise(resolve => setTimeout(resolve, 1200)); // 模拟处理时间

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 模拟创建输出文件
    if (fs.existsSync(inputPath)) {
      fs.copyFileSync(inputPath, outputPath);
    }

    return outputPath;
  }

  /**
   * 裁剪视频
   */
  async trimVideo(inputPath: string, outputPath: string, startTime: number, duration: number): Promise<string> {
    console.log(`[FFmpegService] 模拟裁剪视频: ${inputPath} -> ${outputPath}, 开始: ${startTime}s, 时长: ${duration}s`);

    // TODO: 实现真实的FFmpeg裁剪
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟处理时间

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 模拟创建输出文件
    if (fs.existsSync(inputPath)) {
      fs.copyFileSync(inputPath, outputPath);
    }

    return outputPath;
  }

  /**
   * 检查FFmpeg是否可用
   */
  async checkFFmpegAvailable(): Promise<boolean> {
    console.log(`[FFmpegService] 检查FFmpeg可用性`);

    return new Promise((resolve) => {
      ffmpeg.getAvailableFormats((err, formats) => {
        if (err) {
          console.error(`[FFmpegService] FFmpeg不可用:`, err);
          resolve(false);
        } else {
          console.log(`[FFmpegService] FFmpeg可用，支持${Object.keys(formats || {}).length}种格式`);
          resolve(true);
        }
      });
    });
  }

  /**
   * 获取支持的编解码器
   */
  async getSupportedCodecs(): Promise<string[]> {
    console.log(`[FFmpegService] 获取支持的编解码器`);

    return new Promise((resolve) => {
      ffmpeg.getAvailableCodecs((err, codecs) => {
        if (err) {
          console.error(`[FFmpegService] 获取编解码器失败:`, err);
          resolve(['h264', 'aac', 'mp3']); // 返回默认编解码器
        } else {
          const codecNames = Object.keys(codecs || {});
          resolve(codecNames);
        }
      });
    });
  }

  /**
   * 获取支持的格式
   */
  async getSupportedFormats(): Promise<string[]> {
    console.log(`[FFmpegService] 获取支持的格式`);

    return new Promise((resolve) => {
      ffmpeg.getAvailableFormats((err, formats) => {
        if (err) {
          console.error(`[FFmpegService] 获取格式失败:`, err);
          resolve(['mp4', 'avi', 'mov', 'mp3', 'wav']); // 返回默认格式
        } else {
          const formatNames = Object.keys(formats || {});
          resolve(formatNames);
        }
      });
    });
  }

  /**
   * 注册进度回调
   */
  registerProgressCallback(taskId: string, callback: (progress: any) => void): void {
    this.progressCallbacks.set(taskId, callback);
  }

  /**
   * 移除进度回调
   */
  removeProgressCallback(taskId: string): void {
    this.progressCallbacks.delete(taskId);
  }

  /**
   * 发送进度更新
   */
  private emitProgress(taskId: string | undefined, progress: any): void {
    if (taskId) {
      const callback = this.progressCallbacks.get(taskId);
      if (callback) {
        callback(progress);
      }
    }
    // 同时发送全局事件
    this.emit('progress', { taskId, progress });
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): void {
    const command = this.activeCommands.get(taskId);
    if (command) {
      try {
        command.kill('SIGKILL');
        console.log(`[FFmpegService] 任务已取消: ${taskId}`);
      } catch (error) {
        console.error(`[FFmpegService] 取消任务失败:`, error);
      }
      this.activeCommands.delete(taskId);
      this.progressCallbacks.delete(taskId);
    }
  }

  /**
   * 暂停任务（通过发送SIGSTOP信号）
   */
  pauseTask(taskId: string): void {
    const command = this.activeCommands.get(taskId);
    if (command) {
      try {
        command.kill('SIGSTOP');
        console.log(`[FFmpegService] 任务已暂停: ${taskId}`);
      } catch (error) {
        console.error(`[FFmpegService] 暂停任务失败:`, error);
      }
    }
  }

  /**
   * 恢复任务（通过发送SIGCONT信号）
   */
  resumeTask(taskId: string): void {
    const command = this.activeCommands.get(taskId);
    if (command) {
      try {
        command.kill('SIGCONT');
        console.log(`[FFmpegService] 任务已恢复: ${taskId}`);
      } catch (error) {
        console.error(`[FFmpegService] 恢复任务失败:`, error);
      }
    }
  }

  /**
   * 获取活动任务列表
   */
  getActiveTasks(): string[] {
    return Array.from(this.activeCommands.keys());
  }

  /**
   * 获取FFmpeg路径
   */
  getFFmpegPath(): string {
    return this.ffmpegPath;
  }

  /**
   * 获取FFmpeg版本信息
   */
  async getVersion(): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // 使用命令行方式获取 FFmpeg 版本
        const { execSync } = require('child_process');
        const ffmpegPath = this.ffmpegPath || 'ffmpeg';
        const versionOutput = execSync(`${ffmpegPath} -version`).toString();
        const versionMatch = versionOutput.match(/version\s+(\S+)/i);
        const version = versionMatch ? versionMatch[1] : 'unknown';
        resolve(version);
      } catch (err) {
        console.error('[FFmpegService] 获取版本失败:', err);
        resolve('unknown'); // 返回未知版本而不是拒绝 Promise
      }
    });
  }

  /**
   * 解析时间戳为秒数
   */
  private parseTimestamp(timestamp: string): number {
    if (!timestamp || typeof timestamp !== 'string') {
      return 0;
    }

    // 支持格式: HH:MM:SS.mmm 或 HH:MM:SS
    const parts = timestamp.split(':');
    if (parts.length !== 3) {
      return 0;
    }

    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseFloat(parts[2]) || 0;

    return hours * 3600 + minutes * 60 + seconds;
  }

  /**
   * 解析质量参数
   */
  private parseQualityValue(quality: string): string {
    if (!quality) return '192k';

    // 如果已经包含'k'，直接返回
    if (quality.includes('k')) {
      return quality;
    }

    // 质量级别映射
    const qualityMap: { [key: string]: string } = {
      'high': '320k',
      'standard': '192k',
      'low': '128k'
    };

    // 如果是预定义的质量级别，返回对应的比特率
    if (qualityMap[quality.toLowerCase()]) {
      return qualityMap[quality.toLowerCase()];
    }

    // 如果是纯数字，添加'k'后缀
    if (/^\d+$/.test(quality)) {
      return `${quality}k`;
    }

    // 默认返回标准质量
    return '192k';
  }

  /**
   * 清理所有活动任务
   */
  cleanup(): void {
    console.log(`[FFmpegService] 清理${this.activeCommands.size}个活动任务`);

    for (const [taskId, command] of this.activeCommands) {
      try {
        command.kill('SIGKILL');
      } catch (error) {
        console.error(`[FFmpegService] 清理任务失败: ${taskId}`, error);
      }
    }

    this.activeCommands.clear();
    this.progressCallbacks.clear();
  }
}