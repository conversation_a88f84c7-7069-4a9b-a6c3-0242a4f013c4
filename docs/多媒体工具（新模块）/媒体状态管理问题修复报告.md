# 媒体状态管理问题修复报告

## 问题分析与修复

### 1. 统计数据不实时更新问题

**问题描述：** 视频转换任务执行后，【处理统计】的数据没有实时更新，只有切换到【数据监控】再切换回【多媒体处理】才出现最新数据。

**根本原因：** 任务完成后没有正确触发统计事件总线更新。

**修复方案：**
1. 在 `src/stores/media-tasks.ts` 中导入统计事件总线：
   ```typescript
   import { emitTaskCompleted, emitTaskFailed } from '@/utils/stats-event-bus'
   ```

2. 在任务完成事件处理中添加统计更新触发：
   ```typescript
   // 触发统计更新事件
   emitTaskCompleted(taskId, task.type)
   ```

3. 在任务失败事件处理中添加统计更新触发：
   ```typescript
   // 如果任务失败，触发统计更新事件
   if (status === 'error') {
     const task = state.persistent.singleTasks.get(taskId)
     if (task) {
       emitTaskFailed(taskId, task.type)
     }
   }
   ```

4. 在 `src/stores/media-main.ts` 中也添加失败任务的统计更新触发。

### 2. 处理大小和总耗时显示为0问题

**问题描述：** 【处理统计】的处理大小和总耗时这两个数据一直显示为0。

**根本原因：** TaskResult 中缺少 `size` 字段，且 `processingTime` 可能为空。

**修复方案：**
在任务结果创建时添加文件大小信息：
```typescript
const taskResult: TaskResult = {
  id: generateTaskId(),
  taskId,
  taskType: task.type,
  fileName: task.fileName,
  type: task.type,
  success: true,
  outputPath: result.outputPath,
  completedAt: Date.now(),
  processingTime: result.processingTime || 0, // 确保不为空
  size: task.fileSize || result.size || 0, // 优先使用原文件大小，其次使用结果中的大小
  data: result,
}
```

### 3. 按类型统计缺少成功/失败分类问题

**问题描述：** 按类型统计除了统计每种类型的处理数量，应该还要统计成功和失败各自的数量，同时不需要"按状态统计"这个参数。

**修复方案：**

1. **更新数据结构：** 修改 `ProcessingStats` 接口：
   ```typescript
   export interface ProcessingStats {
     totalProcessed: number
     totalSize: number
     totalTime: number
     successRate: number
     averageProcessingTime: number
     tasksByType: Record<string, { total: number; success: number; failed: number }>
     tasksByStatus: Record<string, number>
     lastUpdated?: number
   }
   ```

2. **更新统计计算逻辑：** 在 `src/stores/media-stats.ts` 中：
   ```typescript
   // 基于结果数据统计类型和状态（只统计结果，不重复计算）
   const resultsByType: Record<string, { total: number; success: number; failed: number }> = {}
   
   allResults.forEach(result => {
     // 统计类型（按成功/失败细分）
     if (result.type) {
       if (!resultsByType[result.type]) {
         resultsByType[result.type] = { total: 0, success: 0, failed: 0 }
       }
       resultsByType[result.type].total += 1
       if (result.success) {
         resultsByType[result.type].success += 1
       } else {
         resultsByType[result.type].failed += 1
       }
     }
   })
   ```

3. **更新UI显示：** 在 `ProcessingStatsPanel.vue` 中：
   ```vue
   <div class="stats-section">
     <h5>按类型统计</h5>
     <div class="stats-grid">
       <div v-for="(typeStats, type) in stats.tasksByType" :key="type" class="stat-item type-stat-item">
         <div class="type-header">
           <Icon :icon="getTypeIcon(type)" class="type-icon" />
           <span class="type-name">{{ getTypeName(type) }}</span>
           <span class="type-total">{{ typeStats.total }}</span>
         </div>
         <div class="type-details">
           <span class="success-count">
             <Icon icon="mdi:check-circle" class="success-icon" />
             成功: {{ typeStats.success }}
           </span>
           <span class="failed-count">
             <Icon icon="mdi:close-circle" class="failed-icon" />
             失败: {{ typeStats.failed }}
           </span>
         </div>
       </div>
     </div>
   </div>
   ```

4. **移除"按状态统计"：** 删除了原来的按状态统计显示部分。

## 修复文件清单

1. `src/stores/media-tasks.ts` - 添加统计事件触发
2. `src/stores/media-main.ts` - 添加失败任务统计事件触发
3. `src/stores/media-stats.ts` - 更新数据结构和统计逻辑
4. `src/components/media/shared/ProcessingStatsPanel.vue` - 更新UI显示和样式

## 预期效果

修复后，媒体处理模块的统计功能将：

1. **实时更新：** 任务完成或失败后立即更新统计数据，无需切换页面
2. **准确显示：** 正确显示处理大小和总耗时数据
3. **详细分类：** 按类型统计时显示总数、成功数和失败数的详细信息
4. **界面优化：** 移除冗余的"按状态统计"，界面更加简洁明了

## 测试建议

1. 执行视频转换任务，观察统计数据是否实时更新
2. 检查处理大小和总耗时是否正确显示
3. 验证按类型统计是否正确显示成功/失败数量
4. 测试任务失败场景下的统计更新
