# 媒体模块状态管理优化方案

## 一、当前状态管理问题分析

### 1.1 状态分散问题
- 组件维护本地状态（fileList、processingOptions等）
- Store 维护全局状态（tasks、results等）
- electron-store 维护持久化状态
- 三层状态之间存在同步延迟和不一致

### 1.2 数据流混乱
- 组件直接调用 IPC
- Store 也调用 IPC
- 状态更新路径不统一
- 缺乏清晰的单向数据流

### 1.3 持久化策略不明确
- 部分数据持久化，部分不持久化
- 持久化时机不确定
- 缺少数据版本管理

## 二、优化方案设计

### 2.1 统一状态管理架构

```
┌─────────────┐     ┌──────────────┐     ┌─────────────────┐
│  Components │────>│ Pinia Stores │<───>│ Electron Store  │
└─────────────┘     └──────────────┘     └─────────────────┘
       ↑                    │                      ↑
       │                    ↓                      │
       └──────────────< Event Bus >───────────────┘
                         (mitt)
```

### 2.2 数据分层设计

#### 第一层：UI临时状态（不持久化）
- 表单输入中的临时值
- UI交互状态（loading、hover等）
- 文件选择器的临时状态

#### 第二层：会话状态（内存中，可选持久化）
- 当前处理中的任务队列
- 实时进度信息
- 临时计算结果

#### 第三层：持久化状态（自动同步到 electron-store）
- 任务历史记录
- 处理结果
- 用户设置
- 统计数据

## 三、具体实现方案

### 3.1 创建持久化插件

```typescript
// src/stores/plugins/persistence.ts
import { PiniaPluginContext } from 'pinia'
import { watch, toRaw } from 'vue'

export interface PersistenceOptions {
  // 持久化配置
  enabled: boolean
  // 存储键名
  key: string
  // 需要持久化的字段
  paths?: string[]
  // 排除的字段
  exclude?: string[]
  // 序列化/反序列化
  serializer?: {
    serialize: (state: any) => string
    deserialize: (value: string) => any
  }
  // 存储策略
  storage?: {
    getItem: (key: string) => Promise<any>
    setItem: (key: string, value: any) => Promise<void>
    removeItem: (key: string) => Promise<void>
  }
  // 防抖延迟（ms）
  debounce?: number
  // 版本管理
  version?: number
  migrate?: (oldState: any, oldVersion: number) => any
}

// Electron IPC 存储适配器
const electronStorage = {
  async getItem(key: string) {
    const response = await window.electronAPI.storage.get(key)
    return response.success ? response.data : null
  },
  
  async setItem(key: string, value: any) {
    await window.electronAPI.storage.set(key, value)
  },
  
  async removeItem(key: string) {
    await window.electronAPI.storage.remove(key)
  }
}

export function createPersistencePlugin(options: PersistenceOptions) {
  return (context: PiniaPluginContext) => {
    const { store } = context
    
    if (!options.enabled) return
    
    const {
      key,
      paths = [],
      exclude = [],
      serializer = {
        serialize: JSON.stringify,
        deserialize: JSON.parse
      },
      storage = electronStorage,
      debounce = 1000,
      version = 1,
      migrate
    } = options
    
    // 恢复持久化数据
    const restore = async () => {
      try {
        const persisted = await storage.getItem(key)
        if (!persisted) return
        
        let state = serializer.deserialize(persisted.data)
        
        // 版本迁移
        if (persisted.version !== version && migrate) {
          state = migrate(state, persisted.version || 0)
        }
        
        // 合并状态
        if (paths.length > 0) {
          paths.forEach(path => {
            setNestedValue(store.$state, path, getNestedValue(state, path))
          })
        } else {
          store.$patch(state)
        }
      } catch (error) {
        console.error(`[Persistence] 恢复状态失败 (${key}):`, error)
      }
    }
    
    // 保存状态（带防抖）
    let saveTimer: NodeJS.Timeout
    const save = () => {
      clearTimeout(saveTimer)
      saveTimer = setTimeout(async () => {
        try {
          const state = toRaw(store.$state)
          let dataToSave = state
          
          // 筛选需要持久化的字段
          if (paths.length > 0) {
            dataToSave = {}
            paths.forEach(path => {
              if (!exclude.includes(path)) {
                setNestedValue(dataToSave, path, getNestedValue(state, path))
              }
            })
          } else if (exclude.length > 0) {
            dataToSave = { ...state }
            exclude.forEach(path => {
              deleteNestedValue(dataToSave, path)
            })
          }
          
          const serialized = serializer.serialize(dataToSave)
          await storage.setItem(key, {
            version,
            data: serialized,
            timestamp: Date.now()
          })
        } catch (error) {
          console.error(`[Persistence] 保存状态失败 (${key}):`, error)
        }
      }, debounce)
    }
    
    // 初始化时恢复数据
    store.$subscribe(() => save())
    restore()
    
    // 提供手动方法
    store.$persist = {
      save,
      restore,
      clear: async () => {
        await storage.removeItem(key)
      }
    }
  }
}

// 工具函数
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((acc, key) => acc?.[key], obj)
}

function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((acc, key) => {
    if (!acc[key]) acc[key] = {}
    return acc[key]
  }, obj)
  target[lastKey] = value
}

function deleteNestedValue(obj: any, path: string): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((acc, key) => acc?.[key], obj)
  if (target) delete target[lastKey]
}
```

### 3.2 改造媒体任务 Store

```typescript
// src/stores/media-tasks.ts
export const useMediaTasksStore = defineStore('media-tasks', () => {
  // 状态定义 - 明确区分持久化和非持久化
  const state = reactive({
    // 持久化数据
    persistent: {
      singleTasks: new Map<string, SingleTask>(),
      batchTasks: new Map<string, BatchTask>(),
      taskResults: new Map<string, TaskResult>(),
      processingHistory: [] as ProcessingRecord[]
    },
    
    // 会话数据（不持久化）
    session: {
      activeTasksCount: 0,
      processingQueues: new Map<string, Set<string>>(),
      taskProgress: new Map<string, number>(),
      currentSteps: new Map<string, string>()
    }
  })
  
  // 计算属性
  const allTasks = computed(() => {
    const singles = Array.from(state.persistent.singleTasks.values())
    const batches = Array.from(state.persistent.batchTasks.values())
      .flatMap(batch => batch.tasks)
    return [...singles, ...batches]
  })
  
  // 使用事件总线替代直接状态修改
  const { mediaEventBus } = useMediaEvents()
  
  // 统一的状态更新方法
  const updateTaskStatus = async (
    taskId: string, 
    status: TaskStatus, 
    progress?: number,
    error?: string
  ) => {
    const task = state.persistent.singleTasks.get(taskId)
    if (!task) return
    
    // 更新状态
    task.status = status
    if (progress !== undefined) task.progress = progress
    if (error) task.error = error
    
    // 更新时间戳
    if (status === 'completed' || status === 'error') {
      task.completedAt = Date.now()
    }
    
    // 发送事件通知
    mediaEventBus.emit('task:status', { taskId, status, error })
    
    // 自动持久化（由插件处理）
  }
  
  // 批量操作优化
  const batchUpdateTasks = async (updates: TaskUpdate[]) => {
    // 使用事务方式批量更新
    const updatedTasks = []
    
    for (const update of updates) {
      const task = state.persistent.singleTasks.get(update.taskId)
      if (task) {
        Object.assign(task, update.changes)
        updatedTasks.push(task)
      }
    }
    
    // 批量发送事件
    mediaEventBus.emit('tasks:batch-update', { tasks: updatedTasks })
  }
  
  return {
    ...toRefs(state.persistent),
    ...toRefs(state.session),
    allTasks,
    updateTaskStatus,
    batchUpdateTasks,
    // ... 其他方法
  }
}, {
  // 应用持久化插件
  persist: {
    enabled: true,
    key: 'media-tasks',
    paths: ['persistent'], // 只持久化 persistent 部分
    storage: electronStorage,
    debounce: 2000, // 2秒防抖
    version: 1,
    migrate: (oldState, oldVersion) => {
      // 版本迁移逻辑
      if (oldVersion < 1) {
        // 转换旧格式到新格式
        return {
          persistent: oldState,
          session: {}
        }
      }
      return oldState
    }
  }
})
```

### 3.3 优化组件状态使用

```typescript
// src/components/media/shared/useMediaProcessor.ts
export function useMediaProcessor(config: MediaProcessorConfig) {
  const tasksStore = useMediaTasksStore()
  const settingsStore = useMediaSettingsStore()
  const { mediaEventBus } = useMediaEvents()
  
  // 完全使用 Store 状态，不维护本地状态
  const fileList = computed({
    get: () => tasksStore.pendingFiles.get(config.taskType) || [],
    set: (files) => tasksStore.setPendingFiles(config.taskType, files)
  })
  
  const processingOptions = computed({
    get: () => tasksStore.taskOptions.get(config.taskType) || config.defaultOptions,
    set: (options) => tasksStore.setTaskOptions(config.taskType, options)
  })
  
  const outputDirectory = computed({
    get: () => settingsStore.outputDirectories.get(config.taskType) || '',
    set: (dir) => settingsStore.setOutputDirectory(config.taskType, dir)
  })
  
  // 监听事件更新界面
  onMounted(() => {
    mediaEventBus.on('task:progress', ({ taskId, progress, currentStep }) => {
      // Store 会自动更新，组件通过计算属性响应
    })
  })
  
  // 任务操作直接委托给 Store
  const startProcessing = async () => {
    const taskIds = await tasksStore.createTasksFromFiles(
      config.taskType,
      fileList.value,
      processingOptions.value,
      outputDirectory.value
    )
    
    // Store 内部处理任务执行和状态更新
    await tasksStore.executeTasks(taskIds)
  }
  
  return {
    fileList,
    processingOptions,
    outputDirectory,
    startProcessing,
    // ... 其他方法
  }
}
```

### 3.4 主进程存储服务

```typescript
// electron/main/services/storage-service.ts
import Store from 'electron-store'
import { app } from 'electron'
import path from 'path'

export class StorageService {
  private stores: Map<string, Store> = new Map()
  private dataPath: string
  
  constructor() {
    this.dataPath = path.join(app.getPath('userData'), 'media-data')
  }
  
  getStore(name: string): Store {
    if (!this.stores.has(name)) {
      this.stores.set(name, new Store({
        name,
        cwd: this.dataPath,
        encryptionKey: process.env.NODE_ENV === 'production' ? 'your-encryption-key' : undefined,
        fileExtension: 'json',
        serialize: (data) => JSON.stringify(data, null, 2),
        deserialize: JSON.parse,
        // 数据迁移
        migrations: {
          '1.0.0': (store) => {
            // 迁移逻辑
          }
        }
      }))
    }
    return this.stores.get(name)!
  }
  
  // 通用存储方法
  async get(storeName: string, key?: string): Promise<any> {
    const store = this.getStore(storeName)
    return key ? store.get(key) : store.store
  }
  
  async set(storeName: string, key: string, value: any): Promise<void> {
    const store = this.getStore(storeName)
    store.set(key, value)
  }
  
  async delete(storeName: string, key: string): Promise<void> {
    const store = this.getStore(storeName)
    store.delete(key)
  }
  
  async clear(storeName: string): Promise<void> {
    const store = this.getStore(storeName)
    store.clear()
  }
  
  // 导出/导入功能
  async exportData(storeName: string, filePath: string): Promise<void> {
    const store = this.getStore(storeName)
    const data = store.store
    await fs.writeJson(filePath, data, { spaces: 2 })
  }
  
  async importData(storeName: string, filePath: string): Promise<void> {
    const data = await fs.readJson(filePath)
    const store = this.getStore(storeName)
    store.store = data
  }
}

// IPC 处理器
export function registerStorageHandlers(ipcMain: Electron.IpcMain, storageService: StorageService) {
  ipcMain.handle('storage-get', async (_, storeName: string, key?: string) => {
    try {
      const data = await storageService.get(storeName, key)
      return { success: true, data }
    } catch (error) {
      return { success: false, error: error.message }
    }
  })
  
  ipcMain.handle('storage-set', async (_, storeName: string, key: string, value: any) => {
    try {
      await storageService.set(storeName, key, value)
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  })
  
  // ... 其他处理器
}
```

## 四、数据冲突避免策略

### 4.1 乐观锁机制

```typescript
// 添加版本控制
interface VersionedData {
  version: number
  data: any
  lastModified: number
}

// Store 中实现
const updateWithVersionCheck = async (key: string, updater: (data: any) => any) => {
  const current = await storage.getItem(key) as VersionedData
  const newData = updater(current.data)
  
  const updated: VersionedData = {
    version: current.version + 1,
    data: newData,
    lastModified: Date.now()
  }
  
  // 原子性更新
  const success = await storage.compareAndSet(key, current.version, updated)
  if (!success) {
    throw new Error('数据版本冲突，请重试')
  }
}
```

### 4.2 操作队列

```typescript
// 任务操作队列，避免并发冲突
class OperationQueue {
  private queue: Array<() => Promise<any>> = []
  private running = false
  
  async enqueue<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await operation()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
      
      if (!this.running) {
        this.processQueue()
      }
    })
  }
  
  private async processQueue() {
    if (this.running || this.queue.length === 0) return
    
    this.running = true
    
    while (this.queue.length > 0) {
      const operation = this.queue.shift()!
      await operation()
    }
    
    this.running = false
  }
}

// 在 Store 中使用
const operationQueue = new OperationQueue()

const updateTask = (taskId: string, changes: any) => {
  return operationQueue.enqueue(async () => {
    // 确保操作串行执行
    await performUpdate(taskId, changes)
  })
}
```

### 4.3 数据分片存储

```typescript
// 大数据量分片存储，避免单文件过大
class ShardedStorage {
  private shardSize = 100 // 每个分片最多100条记录
  
  async saveResults(results: TaskResult[]) {
    const shards = this.createShards(results)
    
    await Promise.all(
      shards.map((shard, index) => 
        storage.setItem(`results-shard-${index}`, shard)
      )
    )
    
    await storage.setItem('results-metadata', {
      shardCount: shards.length,
      totalCount: results.length,
      lastUpdated: Date.now()
    })
  }
  
  async loadResults(): Promise<TaskResult[]> {
    const metadata = await storage.getItem('results-metadata')
    if (!metadata) return []
    
    const shards = await Promise.all(
      Array.from({ length: metadata.shardCount }, (_, i) => 
        storage.getItem(`results-shard-${i}`)
      )
    )
    
    return shards.flat()
  }
  
  private createShards<T>(items: T[]): T[][] {
    const shards: T[][] = []
    for (let i = 0; i < items.length; i += this.shardSize) {
      shards.push(items.slice(i, i + this.shardSize))
    }
    return shards
  }
}
```

## 五、最佳实践建议

### 5.1 状态设计原则
1. **单一数据源**：所有状态都来自 Store
2. **明确分层**：区分持久化和非持久化数据
3. **最小化持久化**：只持久化必要的数据
4. **版本管理**：为持久化数据添加版本控制

### 5.2 性能优化
1. **防抖保存**：避免频繁写入磁盘
2. **增量更新**：只持久化变化的部分
3. **懒加载**：按需加载历史数据
4. **数据压缩**：对大数据进行压缩存储

### 5.3 错误处理
1. **优雅降级**：持久化失败时保持应用可用
2. **数据恢复**：提供数据恢复机制
3. **冲突解决**：自动或手动解决数据冲突
4. **日志记录**：记录所有存储操作

### 5.4 开发体验
1. **TypeScript 类型**：完整的类型定义
2. **开发工具**：Vue DevTools 集成
3. **调试支持**：详细的日志输出
4. **测试支持**：易于测试的架构

## 六、迁移计划

### 第一阶段：基础设施（1周）
- 实现持久化插件
- 创建存储服务
- 设置 IPC 通信

### 第二阶段：Store 改造（1周）
- 重构现有 Store
- 添加持久化配置
- 实现数据迁移

### 第三阶段：组件改造（1周）
- 移除组件本地状态
- 使用 Store 状态
- 集成事件总线

### 第四阶段：测试优化（1周）
- 功能测试
- 性能测试
- 错误处理测试
- 文档更新