# 媒体模块重构实施方案

## 一、概述

本方案针对媒体模块分析报告中发现的问题，提供具体的重构实施步骤。主要目标是解决代码重复、状态同步、功能缺陷和性能问题。

## 二、核心改进策略

### 1. 事件驱动架构替代轮询机制
使用 `mitt` 实现事件总线，替代当前的 `setInterval` 轮询方式。

### 2. 统一状态管理
消除组件本地状态，完全依赖 Store 作为单一数据源，配合 electron-store 实现数据持久化。

### 3. 代码复用最大化
抽取共用逻辑，消除重复代码。

### 4. 数据持久化策略
建立清晰的三层状态管理架构，自动同步持久化数据。

## 三、具体实施步骤

### 第一阶段：建立事件驱动和持久化基础设施

#### 1.1 创建全局事件总线
```typescript
// src/utils/media-event-bus.ts
import mitt from 'mitt'

export type MediaEvents = {
  'task:progress': { taskId: string; progress: number; currentStep?: string }
  'task:status': { taskId: string; status: string; error?: string }
  'task:completed': { taskId: string; result: any }
  'batch:progress': { batchId: string; progress: any }
  'stats:update': void
  'tasks:batch-update': { tasks: any[] }
}

export const mediaEventBus = mitt<MediaEvents>()
```

#### 1.2 创建持久化插件
```typescript
// src/stores/plugins/persistence.ts
import { PiniaPluginContext } from 'pinia'
import { watch, toRaw } from 'vue'

export interface PersistenceOptions {
  enabled: boolean
  key: string
  paths?: string[]
  exclude?: string[]
  serializer?: {
    serialize: (state: any) => string
    deserialize: (value: string) => any
  }
  storage?: {
    getItem: (key: string) => Promise<any>
    setItem: (key: string, value: any) => Promise<void>
    removeItem: (key: string) => Promise<void>
  }
  debounce?: number
  version?: number
  migrate?: (oldState: any, oldVersion: number) => any
}

// Electron IPC 存储适配器
const electronStorage = {
  async getItem(key: string) {
    const response = await window.electronAPI.storage.get(key)
    return response.success ? response.data : null
  },
  
  async setItem(key: string, value: any) {
    await window.electronAPI.storage.set(key, value)
  },
  
  async removeItem(key: string) {
    await window.electronAPI.storage.remove(key)
  }
}

export function createPersistencePlugin(options: PersistenceOptions) {
  return (context: PiniaPluginContext) => {
    const { store } = context
    
    if (!options.enabled) return
    
    const {
      key,
      paths = [],
      exclude = [],
      serializer = {
        serialize: JSON.stringify,
        deserialize: JSON.parse
      },
      storage = electronStorage,
      debounce = 1000,
      version = 1,
      migrate
    } = options
    
    // 恢复持久化数据
    const restore = async () => {
      try {
        const persisted = await storage.getItem(key)
        if (!persisted) return
        
        let state = serializer.deserialize(persisted.data)
        
        // 版本迁移
        if (persisted.version !== version && migrate) {
          state = migrate(state, persisted.version || 0)
        }
        
        // 合并状态
        if (paths.length > 0) {
          paths.forEach(path => {
            setNestedValue(store.$state, path, getNestedValue(state, path))
          })
        } else {
          store.$patch(state)
        }
      } catch (error) {
        console.error(`[Persistence] 恢复状态失败 (${key}):`, error)
      }
    }
    
    // 保存状态（带防抖）
    let saveTimer: NodeJS.Timeout
    const save = () => {
      clearTimeout(saveTimer)
      saveTimer = setTimeout(async () => {
        try {
          const state = toRaw(store.$state)
          let dataToSave = state
          
          // 筛选需要持久化的字段
          if (paths.length > 0) {
            dataToSave = {}
            paths.forEach(path => {
              if (!exclude.includes(path)) {
                setNestedValue(dataToSave, path, getNestedValue(state, path))
              }
            })
          } else if (exclude.length > 0) {
            dataToSave = { ...state }
            exclude.forEach(path => {
              deleteNestedValue(dataToSave, path)
            })
          }
          
          const serialized = serializer.serialize(dataToSave)
          await storage.setItem(key, {
            version,
            data: serialized,
            timestamp: Date.now()
          })
        } catch (error) {
          console.error(`[Persistence] 保存状态失败 (${key}):`, error)
        }
      }, debounce)
    }
    
    // 初始化时恢复数据
    store.$subscribe(() => save())
    restore()
    
    // 提供手动方法
    store.$persist = {
      save,
      restore,
      clear: async () => {
        await storage.removeItem(key)
      }
    }
  }
}

// 工具函数
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((acc, key) => acc?.[key], obj)
}

function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((acc, key) => {
    if (!acc[key]) acc[key] = {}
    return acc[key]
  }, obj)
  target[lastKey] = value
}

function deleteNestedValue(obj: any, path: string): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((acc, key) => acc?.[key], obj)
  if (target) delete target[lastKey]
}
```

#### 1.3 创建主进程存储服务
```typescript
// electron/main/services/storage-service.ts
import Store from 'electron-store'
import { app } from 'electron'
import path from 'path'

export class StorageService {
  private stores: Map<string, Store> = new Map()
  private dataPath: string
  
  constructor() {
    this.dataPath = path.join(app.getPath('userData'), 'media-data')
  }
  
  getStore(name: string): Store {
    if (!this.stores.has(name)) {
      this.stores.set(name, new Store({
        name,
        cwd: this.dataPath,
        encryptionKey: process.env.NODE_ENV === 'production' ? 'your-encryption-key' : undefined,
        fileExtension: 'json',
        serialize: (data) => JSON.stringify(data, null, 2),
        deserialize: JSON.parse,
        migrations: {
          '1.0.0': (store) => {
            // 迁移逻辑
          }
        }
      }))
    }
    return this.stores.get(name)!
  }
  
  async get(storeName: string, key?: string): Promise<any> {
    const store = this.getStore(storeName)
    return key ? store.get(key) : store.store
  }
  
  async set(storeName: string, key: string, value: any): Promise<void> {
    const store = this.getStore(storeName)
    store.set(key, value)
  }
  
  async delete(storeName: string, key: string): Promise<void> {
    const store = this.getStore(storeName)
    store.delete(key)
  }
  
  async clear(storeName: string): Promise<void> {
    const store = this.getStore(storeName)
    store.clear()
  }
  
  // 数据版本控制
  async compareAndSet(storeName: string, key: string, expectedVersion: number, newData: any): Promise<boolean> {
    const store = this.getStore(storeName)
    const current = store.get(key) as any
    
    if (current && current.version !== expectedVersion) {
      return false
    }
    
    store.set(key, newData)
    return true
  }
}

// IPC 处理器
export function registerStorageHandlers(ipcMain: Electron.IpcMain, storageService: StorageService) {
  ipcMain.handle('storage-get', async (_, storeName: string, key?: string) => {
    try {
      const data = await storageService.get(storeName, key)
      return { success: true, data }
    } catch (error) {
      return { success: false, error: error.message }
    }
  })
  
  ipcMain.handle('storage-set', async (_, storeName: string, key: string, value: any) => {
    try {
      await storageService.set(storeName, key, value)
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  })
  
  ipcMain.handle('storage-delete', async (_, storeName: string, key: string) => {
    try {
      await storageService.delete(storeName, key)
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  })
  
  ipcMain.handle('storage-clear', async (_, storeName: string) => {
    try {
      await storageService.clear(storeName)
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  })
}
```

#### 1.4 修改主进程IPC通信
```typescript
// electron/main/ipc/media-handlers.ts
// 在 MediaIPCHandlers 类中添加事件推送

private emitToRenderer(event: string, data: any) {
  BrowserWindow.getAllWindows().forEach(window => {
    if (!window.isDestroyed()) {
      window.webContents.send(`media:${event}`, data)
    }
  })
}

// 修改 setupProgressEvents 方法
setupProgressEvents(): void {
  this.mediaService.onTaskProgress = (taskId: string, progress: number) => {
    this.emitToRenderer('task:progress', { taskId, progress })
  }
  
  this.mediaService.onTaskStatus = (taskId: string, status: string, error?: string) => {
    this.emitToRenderer('task:status', { taskId, status, error })
  }
  
  this.mediaService.onTaskCompleted = (taskId: string, result: any) => {
    this.emitToRenderer('task:completed', { taskId, result })
  }
}
```

#### 1.5 前端接收事件并分发
```typescript
// src/composables/useMediaEvents.ts
import { onMounted, onUnmounted } from 'vue'
import { mediaEventBus } from '@/utils/media-event-bus'

export function useMediaEvents() {
  onMounted(() => {
    // 监听主进程事件
    window.electronAPI.on('media:task:progress', (_, data) => {
      mediaEventBus.emit('task:progress', data)
    })
    
    window.electronAPI.on('media:task:status', (_, data) => {
      mediaEventBus.emit('task:status', data)
    })
    
    window.electronAPI.on('media:task:completed', (_, data) => {
      mediaEventBus.emit('task:completed', data)
    })
  })
  
  onUnmounted(() => {
    // 清理监听器
    window.electronAPI.removeAllListeners('media:task:progress')
    window.electronAPI.removeAllListeners('media:task:status')
    window.electronAPI.removeAllListeners('media:task:completed')
  })
  
  return { mediaEventBus }
}
```

### 第二阶段：重构Store实现统一状态管理

#### 2.1 改造媒体任务Store
```typescript
// src/stores/media-tasks.ts
import { defineStore } from 'pinia'
import { reactive, computed, toRefs } from 'vue'
import { useMediaEvents } from '@/composables/useMediaEvents'

interface TaskUpdate {
  taskId: string
  changes: Partial<SingleTask>
}

export const useMediaTasksStore = defineStore('media-tasks', () => {
  // 状态定义 - 明确区分持久化和非持久化
  const state = reactive({
    // 持久化数据
    persistent: {
      singleTasks: new Map<string, SingleTask>(),
      batchTasks: new Map<string, BatchTask>(),
      taskResults: new Map<string, TaskResult>(),
      processingHistory: [] as ProcessingRecord[],
      // 组件级持久化状态
      pendingFiles: new Map<string, any[]>(),
      taskOptions: new Map<string, ProcessingOptions>()
    },
    
    // 会话数据（不持久化）
    session: {
      activeTasksCount: 0,
      processingQueues: new Map<string, Set<string>>(),
      taskProgress: new Map<string, number>(),
      currentSteps: new Map<string, string>()
    }
  })
  
  // 计算属性
  const allTasks = computed(() => {
    const singles = Array.from(state.persistent.singleTasks.values())
    const batches = Array.from(state.persistent.batchTasks.values())
      .flatMap(batch => batch.tasks)
    return [...singles, ...batches]
  })
  
  const activeTasks = computed(() => {
    return allTasks.value.filter(task => 
      ['pending', 'processing'].includes(task.status)
    )
  })
  
  // 使用事件总线替代直接状态修改
  const { mediaEventBus } = useMediaEvents()
  
  // 统一的状态更新方法
  const updateTaskStatus = async (
    taskId: string, 
    status: TaskStatus, 
    progress?: number,
    error?: string
  ) => {
    const task = state.persistent.singleTasks.get(taskId)
    if (!task) return
    
    // 更新状态
    task.status = status
    if (progress !== undefined) task.progress = progress
    if (error) task.error = error
    
    // 更新时间戳
    if (status === 'completed' || status === 'error') {
      task.completedAt = Date.now()
    }
    
    // 发送事件通知
    mediaEventBus.emit('task:status', { taskId, status, error })
    
    // 自动持久化（由插件处理）
  }
  
  // 批量操作优化
  const batchUpdateTasks = async (updates: TaskUpdate[]) => {
    // 使用事务方式批量更新
    const updatedTasks = []
    
    for (const update of updates) {
      const task = state.persistent.singleTasks.get(update.taskId)
      if (task) {
        Object.assign(task, update.changes)
        updatedTasks.push(task)
      }
    }
    
    // 批量发送事件
    mediaEventBus.emit('tasks:batch-update', { tasks: updatedTasks })
  }
  
  // 组件状态管理
  const setPendingFiles = (taskType: string, files: any[]) => {
    state.persistent.pendingFiles.set(taskType, files)
  }
  
  const setTaskOptions = (taskType: string, options: ProcessingOptions) => {
    state.persistent.taskOptions.set(taskType, options)
  }
  
  // 任务创建和执行
  const createTasksFromFiles = async (
    taskType: string,
    files: any[],
    options: ProcessingOptions,
    outputDirectory: string
  ): Promise<string[]> => {
    const taskIds: string[] = []
    
    for (const file of files) {
      const taskId = generateTaskId()
      const task: SingleTask = {
        id: taskId,
        type: taskType,
        fileName: file.name,
        filePath: file.raw?.path || file.path,
        outputPath: path.join(outputDirectory, generateOutputFileName(file.name, options)),
        options,
        status: 'pending',
        progress: 0,
        createdAt: Date.now()
      }
      
      state.persistent.singleTasks.set(taskId, task)
      taskIds.push(taskId)
    }
    
    return taskIds
  }
  
  const executeTasks = async (taskIds: string[]) => {
    // 委托给主Store执行
    const mainStore = useMediaMainStore()
    
    for (const taskId of taskIds) {
      await mainStore.startSingleTask(taskId)
    }
  }
  
  // 工具方法
  const generateTaskId = () => `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  const generateOutputFileName = (originalName: string, options: ProcessingOptions) => {
    const baseName = path.basename(originalName, path.extname(originalName))
    const ext = options.outputFormat || path.extname(originalName).substr(1)
    return `${baseName}_processed.${ext}`
  }
  
  return {
    ...toRefs(state.persistent),
    ...toRefs(state.session),
    allTasks,
    activeTasks,
    updateTaskStatus,
    batchUpdateTasks,
    setPendingFiles,
    setTaskOptions,
    createTasksFromFiles,
    executeTasks,
    generateTaskId,
    generateOutputFileName
  }
}, {
  // 应用持久化插件
  persist: {
    enabled: true,
    key: 'media-tasks',
    paths: ['persistent'], // 只持久化 persistent 部分
    storage: electronStorage,
    debounce: 2000, // 2秒防抖
    version: 1,
    migrate: (oldState, oldVersion) => {
      // 版本迁移逻辑
      if (oldVersion < 1) {
        // 转换旧格式到新格式
        return {
          persistent: oldState,
          session: {}
        }
      }
      return oldState
    }
  }
})
```

#### 2.2 创建操作队列避免冲突
```typescript
// src/utils/operation-queue.ts
export class OperationQueue {
  private queue: Array<() => Promise<any>> = []
  private running = false
  
  async enqueue<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await operation()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
      
      if (!this.running) {
        this.processQueue()
      }
    })
  }
  
  private async processQueue() {
    if (this.running || this.queue.length === 0) return
    
    this.running = true
    
    while (this.queue.length > 0) {
      const operation = this.queue.shift()!
      await operation()
    }
    
    this.running = false
  }
}
```

### 第三阶段：重构组件消除重复代码

#### 3.1 创建基础媒体处理组件
```typescript
// src/components/media/BaseMediaProcessor.vue
<template>
  <div class="media-processor">
    <div class="section-header">
      <h3>
        <Icon :icon="icon" class="section-icon" />
        {{ title }}
      </h3>
      <slot name="header-actions" />
    </div>

    <FileUploader
      v-model="fileList"
      :task-type="taskType"
      v-bind="uploaderProps"
      @file-added="handleFileAdded"
      @file-removed="handleFileRemoved"
      @error="handleError"
    />

    <div class="options-section" v-if="fileList.length > 0">
      <el-divider>{{ optionsTitle }}</el-divider>
      <ProcessingOptions
        v-model="processingOptions"
        :task-type="taskType"
        v-bind="optionsProps"
      />
      <slot name="extra-options" />
    </div>

    <OutputDirectorySelector
      v-if="fileList.length > 0"
      v-model="outputDirectory"
      v-bind="directoryProps"
    />

    <TaskControls
      v-if="fileList.length > 0"
      v-bind="controlsProps"
      @start="startProcessing"
      @pause="pauseProcessing"
      @stop="stopProcessing"
    />

    <slot name="content" />

    <TaskProgress
      v-if="activeTasks.length > 0"
      :tasks="activeTasks"
      v-bind="progressProps"
    />

    <TaskResultPanel
      v-if="results.length > 0"
      :results="results"
      v-bind="resultProps"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMediaProcessor } from './shared/useMediaProcessor'
import { useMediaEvents } from '@/composables/useMediaEvents'

// Props定义
interface Props {
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  title: string
  icon: string
  optionsTitle?: string
  uploaderProps?: Record<string, any>
  optionsProps?: Record<string, any>
  directoryProps?: Record<string, any>
  controlsProps?: Record<string, any>
  progressProps?: Record<string, any>
  resultProps?: Record<string, any>
  defaultOptions?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  optionsTitle: '处理设置'
})

// 使用组合式函数
const processor = useMediaProcessor({
  taskType: props.taskType,
  defaultOptions: props.defaultOptions || {},
  maxFileSize: props.uploaderProps?.maxFileSize,
  maxFileCount: props.uploaderProps?.maxFileCount,
  supportedFormats: props.uploaderProps?.supportedFormats
})

const { mediaEventBus } = useMediaEvents()

// 监听事件更新任务状态
mediaEventBus.on('task:progress', ({ taskId, progress, currentStep }) => {
  processor.updateTaskProgress(taskId, progress, currentStep)
})

mediaEventBus.on('task:status', ({ taskId, status, error }) => {
  processor.updateTaskStatus(taskId, status, error)
})

mediaEventBus.on('task:completed', ({ taskId, result }) => {
  processor.handleTaskCompleted(taskId, result)
})

// 暴露给父组件的数据
defineExpose({
  ...processor
})
</script>
```

#### 3.2 优化useMediaProcessor组合式函数
```typescript
// src/components/media/shared/useMediaProcessor.ts
export function useMediaProcessor(config: MediaProcessorConfig) {
  const tasksStore = useMediaTasksStore()
  const settingsStore = useMediaSettingsStore()
  const { mediaEventBus } = useMediaEvents()
  
  // 完全使用 Store 状态，不维护本地状态
  const fileList = computed({
    get: () => tasksStore.pendingFiles.get(config.taskType) || [],
    set: (files) => tasksStore.setPendingFiles(config.taskType, files)
  })
  
  const processingOptions = computed({
    get: () => tasksStore.taskOptions.get(config.taskType) || config.defaultOptions,
    set: (options) => tasksStore.setTaskOptions(config.taskType, options)
  })
  
  const outputDirectory = computed({
    get: () => settingsStore.outputDirectories.get(config.taskType) || '',
    set: (dir) => settingsStore.setOutputDirectory(config.taskType, dir)
  })
  
  // 活动任务和结果
  const activeTasks = computed(() => {
    return tasksStore.allTasks.filter(task => 
      task.type === config.taskType &&
      ['pending', 'processing'].includes(task.status)
    )
  })
  
  const results = computed(() => {
    return Array.from(tasksStore.taskResults.values())
      .filter(result => result.taskType === config.taskType)
      .sort((a, b) => b.completedAt - a.completedAt)
  })
  
  // 任务操作直接委托给 Store
  const startProcessing = async () => {
    const taskIds = await tasksStore.createTasksFromFiles(
      config.taskType,
      fileList.value,
      processingOptions.value,
      outputDirectory.value
    )
    
    // Store 内部处理任务执行和状态更新
    await tasksStore.executeTasks(taskIds)
  }
  
  const pauseProcessing = async () => {
    for (const task of activeTasks.value) {
      if (task.status === 'processing') {
        await useMediaMainStore().pauseSingleTask(task.id)
      }
    }
  }
  
  const stopProcessing = async () => {
    for (const task of activeTasks.value) {
      await tasksStore.removeSingleTask(task.id)
    }
    fileList.value = []
  }
  
  // 事件处理方法（Store会自动更新，这里只用于特殊处理）
  const updateTaskProgress = (taskId: string, progress: number, currentStep?: string) => {
    // Store 会自动更新，组件通过计算属性响应
  }
  
  const updateTaskStatus = async (taskId: string, status: string, error?: string) => {
    // Store 会自动更新，组件通过计算属性响应
  }
  
  const handleTaskCompleted = async (taskId: string, result: any) => {
    // 任务完成后，从待处理文件列表中移除
    const task = tasksStore.singleTasks.get(taskId)
    if (task) {
      const fileIndex = fileList.value.findIndex(f => 
        f.raw?.path === task.filePath || f.path === task.filePath
      )
      if (fileIndex > -1) {
        fileList.value.splice(fileIndex, 1)
      }
    }
  }
  
  // 文件处理
  const handleFileAdded = (file: any) => {
    console.log('文件已添加:', file.name)
  }
  
  const handleFileRemoved = (file: any) => {
    console.log('文件已移除:', file.name)
  }
  
  const handleError = (error: string) => {
    ElMessage.error(error)
  }
  
  return {
    fileList,
    processingOptions,
    outputDirectory,
    activeTasks,
    results,
    startProcessing,
    pauseProcessing,
    stopProcessing,
    updateTaskProgress,
    updateTaskStatus,
    handleTaskCompleted,
    handleFileAdded,
    handleFileRemoved,
    handleError
  }
}
```

#### 3.3 简化具体处理组件
```typescript
// src/components/media/VideoConverter.vue
<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="video-convert"
    title="音视频转换"
    icon="mdi:video-outline"
    :uploader-props="{
      maxFileSize: 1024,
      uploadText: '拖拽视频文件到此处或点击上传',
      hint: '支持 MP4, AVI, MOV, MKV, FLV, WMV 格式，最大 1GB'
    }"
    :default-options="{
      outputFormat: 'mp3',
      quality: '192k',
      resizeEnabled: false,
      maxWidth: 1280
    }"
  >
    <template #content>
      <!-- 视频转换特有的内容 -->
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import BaseMediaProcessor from './BaseMediaProcessor.vue'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()

// 视频转换特有的逻辑（如果有）
</script>
```

### 第四阶段：完善功能缺陷

#### 4.1 修复ASR视频处理功能
```typescript
// electron/main/services/asr-service.ts
// 修改 extractAudioFromVideo 方法

private async extractAudioFromVideo(videoPath: string): Promise<string> {
  const ffmpegService = new FFmpegService()
  const fileName = path.basename(videoPath, path.extname(videoPath))
  const audioPath = path.join(this.tempDir, `${fileName}_extracted.wav`)
  
  // 使用FFmpegService提取音频
  await ffmpegService.extractAudio(videoPath, audioPath, {
    quality: 'high'
  })
  
  return audioPath
}
```

#### 4.2 实现FFmpeg未完成的方法
```typescript
// electron/main/services/ffmpeg-service.ts

async extractFrame(videoPath: string, outputPath: string, timeOffset: number = 5): Promise<string> {
  return new Promise((resolve, reject) => {
    ffmpeg(videoPath)
      .screenshots({
        timestamps: [timeOffset],
        filename: path.basename(outputPath),
        folder: path.dirname(outputPath),
        size: '?x720' // 保持宽高比，高度720
      })
      .on('end', () => resolve(outputPath))
      .on('error', reject)
  })
}

async mergeAudioVideo(videoPath: string, audioPath: string, outputPath: string, taskId?: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const command = ffmpeg()
      .input(videoPath)
      .input(audioPath)
      .outputOptions([
        '-c:v copy', // 复制视频流
        '-c:a aac',  // 音频编码为AAC
        '-shortest'  // 以最短的流为准
      ])
      
    if (taskId) {
      this.activeCommands.set(taskId, command)
    }
    
    command
      .on('progress', (progress) => {
        this.emitProgress(taskId, progress)
      })
      .on('end', () => {
        if (taskId) this.activeCommands.delete(taskId)
        resolve(outputPath)
      })
      .on('error', (err) => {
        if (taskId) this.activeCommands.delete(taskId)
        reject(err)
      })
      .save(outputPath)
  })
}

async changeVideoSpeed(inputPath: string, outputPath: string, speed: number, taskId?: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const videoFilter = `setpts=${1/speed}*PTS`
    const audioFilter = `atempo=${speed}`
    
    const command = ffmpeg(inputPath)
      .videoFilters(videoFilter)
      .audioFilters(audioFilter)
      
    if (taskId) {
      this.activeCommands.set(taskId, command)
    }
    
    command
      .on('progress', (progress) => {
        this.emitProgress(taskId, progress)
      })
      .on('end', () => {
        if (taskId) this.activeCommands.delete(taskId)
        resolve(outputPath)
      })
      .on('error', (err) => {
        if (taskId) this.activeCommands.delete(taskId)
        reject(err)
      })
      .save(outputPath)
  })
}
```

### 第五阶段：性能优化

#### 5.1 实现任务队列和并发控制
```typescript
// src/utils/task-queue.ts
export class TaskQueue {
  private queue: Array<() => Promise<any>> = []
  private running = 0
  private concurrency: number
  
  constructor(concurrency: number = 3) {
    this.concurrency = concurrency
  }
  
  async add<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await task()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
      
      this.process()
    })
  }
  
  private async process() {
    if (this.running >= this.concurrency || this.queue.length === 0) {
      return
    }
    
    this.running++
    const task = this.queue.shift()!
    
    try {
      await task()
    } finally {
      this.running--
      this.process()
    }
  }
  
  setConcurrency(concurrency: number) {
    this.concurrency = concurrency
    this.process()
  }
}
```

#### 5.2 批量任务并发处理优化
```typescript
// electron/main/services/media-worker.ts
// 修改 processBatchTasks 方法

private async processBatchTasks(batchId: string, tasks: BatchTask[]): Promise<void> {
  const taskQueue = new TaskQueue(this.batchConcurrency || 3)
  
  // 创建所有任务的Promise
  const taskPromises = tasks.map(task => 
    taskQueue.add(() => this.processSingleTask(task))
  )
  
  // 等待所有任务完成
  await Promise.allSettled(taskPromises)
  
  this.activeTasks.delete(batchId)
  console.log(`[MediaService] 批量任务完成: ${batchId}`)
}

private async processSingleTask(task: BatchTask): Promise<void> {
  task.status = 'processing'
  task.startTime = Date.now()
  
  // 推送状态更新事件
  this.emit('task:status', { taskId: task.id, status: 'processing' })
  
  try {
    // ... 处理逻辑 ...
    
    task.status = 'completed'
    task.progress = 100
    task.endTime = Date.now()
    
    this.emit('task:completed', { taskId: task.id, result: task.result })
  } catch (error: any) {
    task.status = 'error'
    task.error = error.message
    task.endTime = Date.now()
    
    this.emit('task:status', { 
      taskId: task.id, 
      status: 'error', 
      error: error.message 
    })
  }
}
```

#### 5.3 实现数据分片存储
```typescript
// src/utils/sharded-storage.ts
export class ShardedStorage {
  private shardSize = 100 // 每个分片最多100条记录
  
  async saveResults(storeName: string, results: TaskResult[]) {
    const shards = this.createShards(results)
    
    await Promise.all(
      shards.map((shard, index) => 
        window.electronAPI.storage.set(storeName, `results-shard-${index}`, shard)
      )
    )
    
    await window.electronAPI.storage.set(storeName, 'results-metadata', {
      shardCount: shards.length,
      totalCount: results.length,
      lastUpdated: Date.now()
    })
  }
  
  async loadResults(storeName: string): Promise<TaskResult[]> {
    const metadataResponse = await window.electronAPI.storage.get(storeName, 'results-metadata')
    if (!metadataResponse.success || !metadataResponse.data) return []
    
    const metadata = metadataResponse.data
    const shardPromises = Array.from({ length: metadata.shardCount }, (_, i) => 
      window.electronAPI.storage.get(storeName, `results-shard-${i}`)
    )
    
    const shardResponses = await Promise.all(shardPromises)
    const shards = shardResponses
      .filter(r => r.success && r.data)
      .map(r => r.data)
    
    return shards.flat()
  }
  
  private createShards<T>(items: T[]): T[][] {
    const shards: T[][] = []
    for (let i = 0; i < items.length; i += this.shardSize) {
      shards.push(items.slice(i, i + this.shardSize))
    }
    return shards
  }
}
```

### 第六阶段：清理和重组

#### 6.1 删除冗余文件
- 删除 `VideoConverterRefactored.vue`
- 删除 `StatsEventBusTest.vue`
- 清理 `media-store.ts` 中的警告方法

#### 6.2 合并IPC相关文件
将 `media.ts` 的功能合并到 `media-handlers.ts`

#### 6.3 统一错误处理
```typescript
// src/utils/media-error-handler.ts
export class MediaErrorHandler {
  static handle(error: any, context: string): string {
    console.error(`[${context}]`, error)
    
    if (error instanceof Error) {
      return error.message
    }
    
    if (typeof error === 'string') {
      return error
    }
    
    return '未知错误'
  }
  
  static async wrapAsync<T>(
    fn: () => Promise<T>,
    context: string
  ): Promise<{ success: boolean; data?: T; error?: string }> {
    try {
      const data = await fn()
      return { success: true, data }
    } catch (error) {
      return { 
        success: false, 
        error: this.handle(error, context) 
      }
    }
  }
}
```

## 四、实施顺序（更新版）

### 第一阶段（第1周）：基础设施建设
- 实现事件驱动架构（mitt事件总线）
- 创建持久化插件系统
- 建立主进程存储服务
- 设置IPC通信机制

### 第二阶段（第2周）：状态管理重构
- 改造所有媒体相关Store
- 实现三层状态分离
- 配置持久化策略
- 添加版本管理和数据迁移

### 第三阶段（第3周）：组件重构
- 创建BaseMediaProcessor基础组件
- 移除组件本地状态
- 简化具体处理组件
- 集成事件总线

### 第四阶段（第4周）：功能完善
- 修复ASR视频处理
- 实现FFmpeg缺失方法
- 完善错误处理
- 优化用户体验

### 第五阶段（第5周）：性能优化
- 实现任务队列系统
- 添加并发控制
- 优化批量处理
- 实现数据分片存储

### 第六阶段（第6周）：测试和文档
- 全面功能测试
- 性能基准测试
- 清理冗余代码
- 更新技术文档

## 五、风险和注意事项

### 5.1 数据安全
- **数据备份**：重构前备份所有用户数据
- **版本控制**：为持久化数据添加版本号
- **回滚机制**：提供数据恢复功能
- **加密存储**：生产环境启用数据加密

### 5.2 兼容性保证
- **API兼容**：保持公共API不变
- **渐进式迁移**：分阶段实施，每阶段充分测试
- **数据迁移**：自动迁移旧格式数据
- **降级方案**：保留旧代码直到新功能稳定

### 5.3 性能监控
- **基准测试**：记录重构前后性能数据
- **内存监控**：防止内存泄漏
- **存储优化**：监控磁盘使用
- **响应时间**：确保UI响应流畅

## 六、验收标准

### 6.1 功能完整性
- ✅ 所有原有功能正常工作
- ✅ ASR视频处理功能修复
- ✅ FFmpeg所有方法实现
- ✅ 批量处理稳定可靠

### 6.2 代码质量
- ✅ 消除所有代码重复
- ✅ TypeScript类型完整
- ✅ 代码覆盖率 > 80%
- ✅ 无ESLint错误

### 6.3 性能指标
- ✅ 任务处理速度提升 > 30%
- ✅ 内存使用减少 > 20%
- ✅ 启动时间 < 3秒
- ✅ 大批量任务无卡顿

### 6.4 用户体验
- ✅ 实时进度更新流畅
- ✅ 错误信息清晰友好
- ✅ 操作响应及时
- ✅ 数据持久化可靠
