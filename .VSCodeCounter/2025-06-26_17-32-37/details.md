# Details

Date : 2025-06-26 17:32:37

Directory /Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app

Total : 163 files,  36433 codes, 2078 comments, 4948 blanks, all 43459 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.claude/settings.local.json](/.claude/settings.local.json) | JSON | 15 | 0 | 0 | 15 |
| [CLAUDE.local.md](/CLAUDE.local.md) | Markdown | 0 | 0 | 1 | 1 |
| [README.md](/README.md) | Markdown | 3 | 0 | 3 | 6 |
| [api-analysis-report.md](/api-analysis-report.md) | Markdown | 231 | 0 | 63 | 294 |
| [build/config.gypi](/build/config.gypi) | Python | 379 | 1 | 1 | 381 |
| [build/sharp/@img/sharp-darwin-arm64/README.md](/build/sharp/@img/sharp-darwin-arm64/README.md) | Markdown | 13 | 0 | 6 | 19 |
| [build/sharp/@img/sharp-darwin-arm64/package.json](/build/sharp/@img/sharp-darwin-arm64/package.json) | JSON | 40 | 0 | 1 | 41 |
| [build/sharp/@img/sharp-darwin-x64/package-lock.json](/build/sharp/@img/sharp-darwin-x64/package-lock.json) | JSON | 44 | 0 | 1 | 45 |
| [build/sharp/@img/sharp-darwin-x64/package.json](/build/sharp/@img/sharp-darwin-x64/package.json) | JSON | 40 | 0 | 1 | 41 |
| [build/sharp/@img/sharp-libvips-darwin-arm64/README.md](/build/sharp/@img/sharp-libvips-darwin-arm64/README.md) | Markdown | 40 | 0 | 7 | 47 |
| [build/sharp/@img/sharp-libvips-darwin-arm64/lib/glib-2.0/include/glibconfig.h](/build/sharp/@img/sharp-libvips-darwin-arm64/lib/glib-2.0/include/glibconfig.h) | C++ | 153 | 14 | 54 | 221 |
| [build/sharp/@img/sharp-libvips-darwin-arm64/lib/index.js](/build/sharp/@img/sharp-libvips-darwin-arm64/lib/index.js) | JavaScript | 1 | 0 | 1 | 2 |
| [build/sharp/@img/sharp-libvips-darwin-arm64/package.json](/build/sharp/@img/sharp-libvips-darwin-arm64/package.json) | JSON | 36 | 0 | 1 | 37 |
| [build/sharp/@img/sharp-libvips-darwin-arm64/versions.json](/build/sharp/@img/sharp-libvips-darwin-arm64/versions.json) | JSON | 30 | 0 | 0 | 30 |
| [build/sharp/@img/sharp-libvips-darwin-x64/lib/glib-2.0/include/glibconfig.h](/build/sharp/@img/sharp-libvips-darwin-x64/lib/glib-2.0/include/glibconfig.h) | C++ | 154 | 14 | 54 | 222 |
| [build/sharp/@img/sharp-libvips-darwin-x64/lib/index.js](/build/sharp/@img/sharp-libvips-darwin-x64/lib/index.js) | JavaScript | 1 | 0 | 1 | 2 |
| [build/sharp/@img/sharp-libvips-darwin-x64/package-lock.json](/build/sharp/@img/sharp-libvips-darwin-x64/package-lock.json) | JSON | 22 | 0 | 1 | 23 |
| [build/sharp/@img/sharp-libvips-darwin-x64/package.json](/build/sharp/@img/sharp-libvips-darwin-x64/package.json) | JSON | 36 | 0 | 1 | 37 |
| [build/sharp/@img/sharp-libvips-darwin-x64/versions.json](/build/sharp/@img/sharp-libvips-darwin-x64/versions.json) | JSON | 30 | 0 | 0 | 30 |
| [data\_monitor\_design.md](/data_monitor_design.md) | Markdown | 245 | 0 | 33 | 278 |
| [docs/博主分析模块.md](/docs/%E5%8D%9A%E4%B8%BB%E5%88%86%E6%9E%90%E6%A8%A1%E5%9D%97.md) | Markdown | 8 | 0 | 1 | 9 |
| [docs/博主监控界面优化改造.md](/docs/%E5%8D%9A%E4%B8%BB%E7%9B%91%E6%8E%A7%E7%95%8C%E9%9D%A2%E4%BC%98%E5%8C%96%E6%94%B9%E9%80%A0.md) | Markdown | 30 | 0 | 6 | 36 |
| [docs/多媒体工具（新模块）/博主监控模块完善版规划.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%8D%9A%E4%B8%BB%E7%9B%91%E6%8E%A7%E6%A8%A1%E5%9D%97%E5%AE%8C%E5%96%84%E7%89%88%E8%A7%84%E5%88%92.md) | Markdown | 489 | 0 | 130 | 619 |
| [docs/多媒体工具（新模块）/媒体模块分析报告.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E6%A8%A1%E5%9D%97%E5%88%86%E6%9E%90%E6%8A%A5%E5%91%8A.md) | Markdown | 165 | 0 | 58 | 223 |
| [docs/多媒体工具（新模块）/媒体模块状态管理优化方案.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E6%A8%A1%E5%9D%97%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86%E4%BC%98%E5%8C%96%E6%96%B9%E6%A1%88.md) | Markdown | 555 | 0 | 105 | 660 |
| [docs/多媒体工具（新模块）/媒体模块重构实施方案.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E6%A8%A1%E5%9D%97%E9%87%8D%E6%9E%84%E5%AE%9E%E6%96%BD%E6%96%B9%E6%A1%88.md) | Markdown | 1,058 | 0 | 198 | 1,256 |
| [docs/多媒体工具（新模块）/媒体状态管理问题修复报告.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86%E9%97%AE%E9%A2%98%E4%BF%AE%E5%A4%8D%E6%8A%A5%E5%91%8A.md) | Markdown | 123 | 0 | 28 | 151 |
| [docs/多媒体工具（新模块）/新模块设计方案.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E6%96%B0%E6%A8%A1%E5%9D%97%E8%AE%BE%E8%AE%A1%E6%96%B9%E6%A1%88.md) | Markdown | 582 | 0 | 83 | 665 |
| [docs/多媒体工具（新模块）/模块集成完成报告.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E6%A8%A1%E5%9D%97%E9%9B%86%E6%88%90%E5%AE%8C%E6%88%90%E6%8A%A5%E5%91%8A.md) | Markdown | 166 | 0 | 49 | 215 |
| [docs/多媒体工具（新模块）/编码修改完成总结.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E7%BC%96%E7%A0%81%E4%BF%AE%E6%94%B9%E5%AE%8C%E6%88%90%E6%80%BB%E7%BB%93.md) | Markdown | 124 | 0 | 27 | 151 |
| [docs/多媒体工具（新模块）/视频处理组件后端转换方法完善方案.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E8%A7%86%E9%A2%91%E5%A4%84%E7%90%86%E7%BB%84%E4%BB%B6%E5%90%8E%E7%AB%AF%E8%BD%AC%E6%8D%A2%E6%96%B9%E6%B3%95%E5%AE%8C%E5%96%84%E6%96%B9%E6%A1%88.md) | Markdown | 394 | 0 | 105 | 499 |
| [docs/多媒体工具（新模块）/问题修复/目前存在的问题.md](/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E9%97%AE%E9%A2%98%E4%BF%AE%E5%A4%8D/%E7%9B%AE%E5%89%8D%E5%AD%98%E5%9C%A8%E7%9A%84%E9%97%AE%E9%A2%98.md) | Markdown | 18 | 0 | 8 | 26 |
| [docs/拆解数据.md](/docs/%E6%8B%86%E8%A7%A3%E6%95%B0%E6%8D%AE.md) | Markdown | 155 | 0 | 29 | 184 |
| [docs/爆文拆解功能.md](/docs/%E7%88%86%E6%96%87%E6%8B%86%E8%A7%A3%E5%8A%9F%E8%83%BD.md) | Markdown | 0 | 0 | 1 | 1 |
| [docs/项目架构与功能模块总结.md](/docs/%E9%A1%B9%E7%9B%AE%E6%9E%B6%E6%9E%84%E4%B8%8E%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97%E6%80%BB%E7%BB%93.md) | Markdown | 92 | 0 | 33 | 125 |
| [electron-builder.yml](/electron-builder.yml) | YAML | 53 | 12 | 12 | 77 |
| [electron/main/api/base-api.ts](/electron/main/api/base-api.ts) | TypeScript | 252 | 60 | 42 | 354 |
| [electron/main/api/cache-manager.ts](/electron/main/api/cache-manager.ts) | TypeScript | 153 | 45 | 38 | 236 |
| [electron/main/api/config-manager.ts](/electron/main/api/config-manager.ts) | TypeScript | 204 | 86 | 55 | 345 |
| [electron/main/api/error-types.ts](/electron/main/api/error-types.ts) | TypeScript | 132 | 44 | 20 | 196 |
| [electron/main/api/models/api-types.ts](/electron/main/api/models/api-types.ts) | TypeScript | 93 | 4 | 7 | 104 |
| [electron/main/api/models/note.ts](/electron/main/api/models/note.ts) | TypeScript | 122 | 2 | 12 | 136 |
| [electron/main/api/models/type-mappings.ts](/electron/main/api/models/type-mappings.ts) | TypeScript | 149 | 25 | 10 | 184 |
| [electron/main/api/models/unified-types.ts](/electron/main/api/models/unified-types.ts) | TypeScript | 131 | 23 | 22 | 176 |
| [electron/main/api/models/web-types.ts](/electron/main/api/models/web-types.ts) | TypeScript | 37 | 0 | 0 | 37 |
| [electron/main/api/web-xhs-api.ts](/electron/main/api/web-xhs-api.ts) | TypeScript | 380 | 56 | 62 | 498 |
| [electron/main/api/xhs-api.ts](/electron/main/api/xhs-api.ts) | TypeScript | 86 | 10 | 22 | 118 |
| [electron/main/api/xhs-spider.ts](/electron/main/api/xhs-spider.ts) | TypeScript | 210 | 103 | 33 | 346 |
| [electron/main/api/xhs-word-api.ts](/electron/main/api/xhs-word-api.ts) | TypeScript | 30 | 2 | 10 | 42 |
| [electron/main/appState.ts](/electron/main/appState.ts) | TypeScript | 8 | 0 | 2 | 10 |
| [electron/main/index.ts](/electron/main/index.ts) | TypeScript | 198 | 37 | 26 | 261 |
| [electron/main/ipc/app.ts](/electron/main/ipc/app.ts) | TypeScript | 67 | 8 | 10 | 85 |
| [electron/main/ipc/excel.ts](/electron/main/ipc/excel.ts) | TypeScript | 19 | 0 | 3 | 22 |
| [electron/main/ipc/fileSystem.ts](/electron/main/ipc/fileSystem.ts) | TypeScript | 53 | 2 | 8 | 63 |
| [electron/main/ipc/media-handlers.ts](/electron/main/ipc/media-handlers.ts) | TypeScript | 341 | 46 | 64 | 451 |
| [electron/main/ipc/media.ts](/electron/main/ipc/media.ts) | TypeScript | 35 | 11 | 7 | 53 |
| [electron/main/ipc/monitor.ts](/electron/main/ipc/monitor.ts) | TypeScript | 47 | 1 | 12 | 60 |
| [electron/main/ipc/updater.ts](/electron/main/ipc/updater.ts) | TypeScript | 21 | 0 | 3 | 24 |
| [electron/main/ipc/xhs.ts](/electron/main/ipc/xhs.ts) | TypeScript | 124 | 1 | 13 | 138 |
| [electron/main/ipcHandlers.ts](/electron/main/ipcHandlers.ts) | TypeScript | 41 | 9 | 9 | 59 |
| [electron/main/libs/code.d.ts](/electron/main/libs/code.d.ts) | TypeScript | 1 | 0 | 0 | 1 |
| [electron/main/libs/code.js](/electron/main/libs/code.js) | JavaScript | 43 | 0 | 9 | 52 |
| [electron/main/services/asr-service.ts](/electron/main/services/asr-service.ts) | TypeScript | 272 | 61 | 61 | 394 |
| [electron/main/services/blogger-monitor-task.ts](/electron/main/services/blogger-monitor-task.ts) | TypeScript | 223 | 24 | 37 | 284 |
| [electron/main/services/excel-worker.ts](/electron/main/services/excel-worker.ts) | TypeScript | 56 | 22 | 10 | 88 |
| [electron/main/services/ffmpeg-service.ts](/electron/main/services/ffmpeg-service.ts) | TypeScript | 412 | 109 | 88 | 609 |
| [electron/main/services/media-worker.ts](/electron/main/services/media-worker.ts) | TypeScript | 369 | 101 | 92 | 562 |
| [electron/main/services/monitor-service.ts](/electron/main/services/monitor-service.ts) | TypeScript | 288 | 25 | 36 | 349 |
| [electron/main/services/note-monitor-task.ts](/electron/main/services/note-monitor-task.ts) | TypeScript | 186 | 21 | 29 | 236 |
| [electron/main/services/storage-service.ts](/electron/main/services/storage-service.ts) | TypeScript | 86 | 3 | 14 | 103 |
| [electron/main/services/xhs-worker.ts](/electron/main/services/xhs-worker.ts) | TypeScript | 165 | 97 | 39 | 301 |
| [electron/main/store/monitor-store.ts](/electron/main/store/monitor-store.ts) | TypeScript | 261 | 69 | 34 | 364 |
| [electron/main/updater.ts](/electron/main/updater.ts) | TypeScript | 177 | 35 | 31 | 243 |
| [electron/main/utils/asr/ASRData.ts](/electron/main/utils/asr/ASRData.ts) | TypeScript | 115 | 37 | 20 | 172 |
| [electron/main/utils/asr/ASRDataSeg.ts](/electron/main/utils/asr/ASRDataSeg.ts) | TypeScript | 38 | 19 | 8 | 65 |
| [electron/main/utils/asr/BaseASR.ts](/electron/main/utils/asr/BaseASR.ts) | TypeScript | 156 | 39 | 34 | 229 |
| [electron/main/utils/asr/BcutASR.ts](/electron/main/utils/asr/BcutASR.ts) | TypeScript | 265 | 36 | 56 | 357 |
| [electron/main/utils/code-utils.ts](/electron/main/utils/code-utils.ts) | TypeScript | 128 | 1 | 9 | 138 |
| [electron/main/utils/header-utils.ts](/electron/main/utils/header-utils.ts) | TypeScript | 50 | 1 | 8 | 59 |
| [electron/main/utils/img-utils.ts](/electron/main/utils/img-utils.ts) | TypeScript | 179 | 33 | 31 | 243 |
| [electron/main/utils/number-utils.ts](/electron/main/utils/number-utils.ts) | TypeScript | 41 | 20 | 12 | 73 |
| [electron/main/utils/object-utils.ts](/electron/main/utils/object-utils.ts) | TypeScript | 46 | 30 | 17 | 93 |
| [electron/main/utils/sleep.ts](/electron/main/utils/sleep.ts) | TypeScript | 16 | 9 | 3 | 28 |
| [electron/main/utils/transform-utils.ts](/electron/main/utils/transform-utils.ts) | TypeScript | 165 | 52 | 23 | 240 |
| [electron/main/utils/url-utils.ts](/electron/main/utils/url-utils.ts) | TypeScript | 69 | 28 | 15 | 112 |
| [electron/main/utils/wdj-proxy.ts](/electron/main/utils/wdj-proxy.ts) | TypeScript | 35 | 19 | 12 | 66 |
| [electron/main/windows.ts](/electron/main/windows.ts) | TypeScript | 249 | 37 | 43 | 329 |
| [electron/preload/index.ts](/electron/preload/index.ts) | TypeScript | 165 | 37 | 20 | 222 |
| [electron\_xhs\_user\_notes\_no\_puppeteer.md](/electron_xhs_user_notes_no_puppeteer.md) | Markdown | 96 | 0 | 23 | 119 |
| [functional-migration-plan.md](/functional-migration-plan.md) | Markdown | 61 | 0 | 18 | 79 |
| [index.html](/index.html) | HTML | 13 | 0 | 3 | 16 |
| [ipc\_refactoring\_plan.md](/ipc_refactoring_plan.md) | Markdown | 55 | 0 | 11 | 66 |
| [logs.md](/logs.md) | Markdown | 135 | 0 | 59 | 194 |
| [media-module-refactor-design.md](/media-module-refactor-design.md) | Markdown | 1,411 | 0 | 231 | 1,642 |
| [monitor-refactor-plan.md](/monitor-refactor-plan.md) | Markdown | 335 | 0 | 113 | 448 |
| [package-lock.json](/package-lock.json) | JSON | 8,483 | 0 | 1 | 8,484 |
| [package.json](/package.json) | JSON | 55 | 0 | 1 | 56 |
| [project\_summary.md](/project_summary.md) | Markdown | 142 | 0 | 13 | 155 |
| [public/html/biaoqing.html](/public/html/biaoqing.html) | HTML | 122 | 0 | 0 | 122 |
| [public/vite.svg](/public/vite.svg) | XML | 1 | 0 | 0 | 1 |
| [src/App.vue](/src/App.vue) | vue | 400 | 15 | 67 | 482 |
| [src/assets/logo.svg](/src/assets/logo.svg) | XML | 13 | 6 | 5 | 24 |
| [src/assets/styles/global.scss](/src/assets/styles/global.scss) | SCSS | 286 | 37 | 60 | 383 |
| [src/assets/styles/index.scss](/src/assets/styles/index.scss) | SCSS | 1 | 0 | 1 | 2 |
| [src/assets/vue.svg](/src/assets/vue.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/ProgressBar.vue](/src/components/ProgressBar.vue) | vue | 65 | 0 | 10 | 75 |
| [src/components/blogger-monitor/AddBloggerTaskDialog.vue](/src/components/blogger-monitor/AddBloggerTaskDialog.vue) | vue | 243 | 1 | 32 | 276 |
| [src/components/blogger-monitor/BloggerTaskList.vue](/src/components/blogger-monitor/BloggerTaskList.vue) | vue | 486 | 12 | 66 | 564 |
| [src/components/blogger-monitor/BloggerWorksList.vue](/src/components/blogger-monitor/BloggerWorksList.vue) | vue | 427 | 15 | 63 | 505 |
| [src/components/media/ASRProcessor.vue](/src/components/media/ASRProcessor.vue) | vue | 93 | 2 | 11 | 106 |
| [src/components/media/BaseMediaProcessor.vue](/src/components/media/BaseMediaProcessor.vue) | vue | 105 | 0 | 20 | 125 |
| [src/components/media/ImageProcessor.vue](/src/components/media/ImageProcessor.vue) | vue | 143 | 2 | 17 | 162 |
| [src/components/media/VideoConverter.vue](/src/components/media/VideoConverter.vue) | vue | 35 | 1 | 5 | 41 |
| [src/components/media/shared/BaseMediaProcessor.vue](/src/components/media/shared/BaseMediaProcessor.vue) | vue | 290 | 9 | 42 | 341 |
| [src/components/media/shared/FileUploader.vue](/src/components/media/shared/FileUploader.vue) | vue | 264 | 1 | 44 | 309 |
| [src/components/media/shared/GlobalTaskList.vue](/src/components/media/shared/GlobalTaskList.vue) | vue | 275 | 0 | 41 | 316 |
| [src/components/media/shared/MediaStatsCard.vue](/src/components/media/shared/MediaStatsCard.vue) | vue | 189 | 0 | 28 | 217 |
| [src/components/media/shared/OutputDirectorySelector.vue](/src/components/media/shared/OutputDirectorySelector.vue) | vue | 212 | 1 | 40 | 253 |
| [src/components/media/shared/ProcessingOptions.vue](/src/components/media/shared/ProcessingOptions.vue) | vue | 428 | 8 | 59 | 495 |
| [src/components/media/shared/ProcessingStatsPanel.vue](/src/components/media/shared/ProcessingStatsPanel.vue) | vue | 588 | 4 | 95 | 687 |
| [src/components/media/shared/TaskControls.vue](/src/components/media/shared/TaskControls.vue) | vue | 382 | 2 | 59 | 443 |
| [src/components/media/shared/TaskListItem.vue](/src/components/media/shared/TaskListItem.vue) | vue | 267 | 0 | 44 | 311 |
| [src/components/media/shared/TaskProgress.vue](/src/components/media/shared/TaskProgress.vue) | vue | 463 | 7 | 71 | 541 |
| [src/components/media/shared/TaskResultPanel.vue](/src/components/media/shared/TaskResultPanel.vue) | vue | 672 | 14 | 101 | 787 |
| [src/components/media/shared/\_media-common.scss](/src/components/media/shared/_media-common.scss) | SCSS | 359 | 20 | 72 | 451 |
| [src/components/media/shared/useMediaProcessor.ts](/src/components/media/shared/useMediaProcessor.ts) | TypeScript | 185 | 15 | 31 | 231 |
| [src/components/note-monitor/AddNoteTaskDialog.vue](/src/components/note-monitor/AddNoteTaskDialog.vue) | vue | 115 | 0 | 16 | 131 |
| [src/components/note-monitor/EditNoteTaskDialog.vue](/src/components/note-monitor/EditNoteTaskDialog.vue) | vue | 122 | 0 | 14 | 136 |
| [src/components/note-monitor/NoteTaskList.vue](/src/components/note-monitor/NoteTaskList.vue) | vue | 346 | 7 | 45 | 398 |
| [src/composables/useMediaEvents.ts](/src/composables/useMediaEvents.ts) | TypeScript | 14 | 7 | 2 | 23 |
| [src/main.ts](/src/main.ts) | TypeScript | 17 | 4 | 5 | 26 |
| [src/router/index.ts](/src/router/index.ts) | TypeScript | 56 | 0 | 4 | 60 |
| [src/stores/index.ts](/src/stores/index.ts) | TypeScript | 10 | 4 | 5 | 19 |
| [src/stores/media-main.ts](/src/stores/media-main.ts) | TypeScript | 232 | 34 | 46 | 312 |
| [src/stores/media-settings.ts](/src/stores/media-settings.ts) | TypeScript | 181 | 21 | 35 | 237 |
| [src/stores/media-stats.ts](/src/stores/media-stats.ts) | TypeScript | 183 | 32 | 43 | 258 |
| [src/stores/media-tasks.ts](/src/stores/media-tasks.ts) | TypeScript | 328 | 39 | 81 | 448 |
| [src/stores/plugins/persistence.ts](/src/stores/plugins/persistence.ts) | TypeScript | 225 | 26 | 39 | 290 |
| [src/style.css](/src/style.css) | CSS | 70 | 0 | 10 | 80 |
| [src/utils/media-event-bus.ts](/src/utils/media-event-bus.ts) | TypeScript | 12 | 3 | 4 | 19 |
| [src/utils/media-event-manager.ts](/src/utils/media-event-manager.ts) | TypeScript | 94 | 26 | 23 | 143 |
| [src/utils/operation-queue.ts](/src/utils/operation-queue.ts) | TypeScript | 28 | 0 | 6 | 34 |
| [src/utils/stats-event-bus.ts](/src/utils/stats-event-bus.ts) | TypeScript | 32 | 3 | 8 | 43 |
| [src/views/Batch.vue](/src/views/Batch.vue) | vue | 443 | 4 | 67 | 514 |
| [src/views/BloggerMonitor.vue](/src/views/BloggerMonitor.vue) | vue | 94 | 3 | 18 | 115 |
| [src/views/Check.vue](/src/views/Check.vue) | vue | 262 | 2 | 43 | 307 |
| [src/views/Home.vue](/src/views/Home.vue) | vue | 560 | 12 | 94 | 666 |
| [src/views/Media.vue](/src/views/Media.vue) | vue | 577 | 7 | 96 | 680 |
| [src/views/Monitor.vue](/src/views/Monitor.vue) | vue | 26 | 0 | 3 | 29 |
| [src/views/Note.vue](/src/views/Note.vue) | vue | 632 | 8 | 108 | 748 |
| [src/views/NoteMonitor.vue](/src/views/NoteMonitor.vue) | vue | 104 | 3 | 16 | 123 |
| [src/views/NoteMonitorData.vue](/src/views/NoteMonitorData.vue) | vue | 272 | 3 | 33 | 308 |
| [src/views/UserCheck.vue](/src/views/UserCheck.vue) | vue | 385 | 3 | 68 | 456 |
| [src/vite-env.d.ts](/src/vite-env.d.ts) | TypeScript | 212 | 19 | 31 | 262 |
| [stealth.min.js](/stealth.min.js) | JavaScript | 1 | 6 | 0 | 7 |
| [tsconfig.app.json](/tsconfig.app.json) | JSON | 23 | 1 | 0 | 24 |
| [tsconfig.json](/tsconfig.json) | JSON with Comments | 7 | 0 | 1 | 8 |
| [tsconfig.node.json](/tsconfig.node.json) | JSON | 35 | 3 | 0 | 38 |
| [vite.config.ts](/vite.config.ts) | TypeScript | 70 | 7 | 3 | 80 |
| [vscode/.debug.script.mjs](/vscode/.debug.script.mjs) | JavaScript | 78 | 32 | 18 | 128 |
| [vscode/launch.json](/vscode/launch.json) | JSON with Comments | 53 | 6 | 0 | 59 |
| [优化.md](/%E4%BC%98%E5%8C%96.md) | Markdown | 12 | 0 | 8 | 20 |
| [未使用组件和代码分析.md](/%E6%9C%AA%E4%BD%BF%E7%94%A8%E7%BB%84%E4%BB%B6%E5%92%8C%E4%BB%A3%E7%A0%81%E5%88%86%E6%9E%90.md) | Markdown | 27 | 0 | 14 | 41 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)