"filename", "language", "TypeScript", "SCSS", "XML", "vue", "CSS", "JSON with Comments", "JavaScript", "Markdown", "YAML", "JSON", "HTML", "Python", "C++", "comment", "blank", "total"
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/.claude/settings.local.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 15
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/CLAUDE.local.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/README.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 3, 6
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/api-analysis-report.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 231, 0, 0, 0, 0, 0, 0, 63, 294
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/config.gypi", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 379, 0, 1, 1, 381
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-darwin-arm64/README.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 6, 19
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-darwin-arm64/package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 1, 41
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-darwin-x64/package-lock.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 1, 45
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-darwin-x64/package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 1, 41
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-arm64/README.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 7, 47
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-arm64/lib/glib-2.0/include/glibconfig.h", "C++", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 14, 54, 221
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-arm64/lib/index.js", "JavaScript", 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-arm64/package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 0, 0, 0, 1, 37
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-arm64/versions.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 30
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-x64/lib/glib-2.0/include/glibconfig.h", "C++", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 154, 14, 54, 222
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-x64/lib/index.js", "JavaScript", 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-x64/package-lock.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 1, 23
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-x64/package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 0, 0, 0, 1, 37
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/@img/sharp-libvips-darwin-x64/versions.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 30
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/data_monitor_design.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 245, 0, 0, 0, 0, 0, 0, 33, 278
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/博主分析模块.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 1, 9
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/博主监控界面优化改造.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 6, 36
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/博主监控模块完善版规划.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 489, 0, 0, 0, 0, 0, 0, 130, 619
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/媒体模块分析报告.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 165, 0, 0, 0, 0, 0, 0, 58, 223
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/媒体模块状态管理优化方案.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 555, 0, 0, 0, 0, 0, 0, 105, 660
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/媒体模块重构实施方案.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 1058, 0, 0, 0, 0, 0, 0, 198, 1256
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/媒体状态管理问题修复报告.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 28, 151
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/新模块设计方案.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 582, 0, 0, 0, 0, 0, 0, 83, 665
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/模块集成完成报告.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 166, 0, 0, 0, 0, 0, 0, 49, 215
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/编码修改完成总结.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 124, 0, 0, 0, 0, 0, 0, 27, 151
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/视频处理组件后端转换方法完善方案.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 394, 0, 0, 0, 0, 0, 0, 105, 499
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/多媒体工具（新模块）/问题修复/目前存在的问题.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 8, 26
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/拆解数据.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 29, 184
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/爆文拆解功能.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/项目架构与功能模块总结.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 0, 33, 125
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron-builder.yml", "YAML", 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 12, 12, 77
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/base-api.ts", "TypeScript", 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 42, 354
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/cache-manager.ts", "TypeScript", 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 38, 236
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/config-manager.ts", "TypeScript", 204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 86, 55, 345
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/error-types.ts", "TypeScript", 132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 20, 196
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/api-types.ts", "TypeScript", 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 7, 104
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/note.ts", "TypeScript", 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12, 136
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/type-mappings.ts", "TypeScript", 149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 10, 184
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/unified-types.ts", "TypeScript", 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 22, 176
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/web-types.ts", "TypeScript", 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/web-xhs-api.ts", "TypeScript", 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 62, 498
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/xhs-api.ts", "TypeScript", 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 22, 118
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/xhs-spider.ts", "TypeScript", 210, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 103, 33, 346
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/xhs-word-api.ts", "TypeScript", 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 42
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/appState.ts", "TypeScript", 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/index.ts", "TypeScript", 198, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 26, 261
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/app.ts", "TypeScript", 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 10, 85
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/excel.ts", "TypeScript", 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 22
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/fileSystem.ts", "TypeScript", 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8, 63
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/media-handlers.ts", "TypeScript", 341, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 64, 451
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/media.ts", "TypeScript", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 7, 53
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/monitor.ts", "TypeScript", 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 12, 60
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/updater.ts", "TypeScript", 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 24
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/xhs.ts", "TypeScript", 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13, 138
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipcHandlers.ts", "TypeScript", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 59
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/libs/code.d.ts", "TypeScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/libs/code.js", "JavaScript", 0, 0, 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 9, 52
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/asr-service.ts", "TypeScript", 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 61, 394
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/blogger-monitor-task.ts", "TypeScript", 223, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 37, 284
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/excel-worker.ts", "TypeScript", 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 10, 88
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/ffmpeg-service.ts", "TypeScript", 412, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 109, 88, 609
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/media-worker.ts", "TypeScript", 369, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 92, 562
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/monitor-service.ts", "TypeScript", 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 36, 349
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/note-monitor-task.ts", "TypeScript", 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 29, 236
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/storage-service.ts", "TypeScript", 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 14, 103
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/xhs-worker.ts", "TypeScript", 165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 39, 301
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/store/monitor-store.ts", "TypeScript", 261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 34, 364
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/updater.ts", "TypeScript", 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 31, 243
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/ASRData.ts", "TypeScript", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 20, 172
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/ASRDataSeg.ts", "TypeScript", 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 8, 65
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/BaseASR.ts", "TypeScript", 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 34, 229
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/BcutASR.ts", "TypeScript", 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 56, 357
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/code-utils.ts", "TypeScript", 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 138
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/header-utils.ts", "TypeScript", 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 8, 59
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/img-utils.ts", "TypeScript", 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 31, 243
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/number-utils.ts", "TypeScript", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 12, 73
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/object-utils.ts", "TypeScript", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 17, 93
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/sleep.ts", "TypeScript", 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 28
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/transform-utils.ts", "TypeScript", 165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 23, 240
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/url-utils.ts", "TypeScript", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 15, 112
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/wdj-proxy.ts", "TypeScript", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 12, 66
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/windows.ts", "TypeScript", 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 43, 329
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/preload/index.ts", "TypeScript", 165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 20, 222
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron_xhs_user_notes_no_puppeteer.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 23, 119
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/functional-migration-plan.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 18, 79
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 3, 16
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/ipc_refactoring_plan.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 11, 66
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/logs.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 135, 0, 0, 0, 0, 0, 0, 59, 194
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/media-module-refactor-design.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 1411, 0, 0, 0, 0, 0, 0, 231, 1642
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/monitor-refactor-plan.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 335, 0, 0, 0, 0, 0, 0, 113, 448
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/package-lock.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 8483, 0, 0, 0, 0, 1, 8484
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 1, 56
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/project_summary.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 142, 0, 0, 0, 0, 0, 0, 13, 155
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/public/html/biaoqing.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 122, 0, 0, 0, 0, 122
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/public/vite.svg", "XML", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/App.vue", "vue", 0, 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 67, 482
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/logo.svg", "XML", 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 5, 24
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/styles/global.scss", "SCSS", 0, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 60, 383
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/styles/index.scss", "SCSS", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/vue.svg", "XML", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/ProgressBar.vue", "vue", 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 75
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/blogger-monitor/AddBloggerTaskDialog.vue", "vue", 0, 0, 0, 243, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 32, 276
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/blogger-monitor/BloggerTaskList.vue", "vue", 0, 0, 0, 486, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 66, 564
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/blogger-monitor/BloggerWorksList.vue", "vue", 0, 0, 0, 427, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 63, 505
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/ASRProcessor.vue", "vue", 0, 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11, 106
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/BaseMediaProcessor.vue", "vue", 0, 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 125
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/ImageProcessor.vue", "vue", 0, 0, 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 17, 162
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/VideoConverter.vue", "vue", 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5, 41
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/BaseMediaProcessor.vue", "vue", 0, 0, 0, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 42, 341
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/FileUploader.vue", "vue", 0, 0, 0, 264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 44, 309
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/GlobalTaskList.vue", "vue", 0, 0, 0, 275, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 316
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/MediaStatsCard.vue", "vue", 0, 0, 0, 189, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 217
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/OutputDirectorySelector.vue", "vue", 0, 0, 0, 212, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 40, 253
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/ProcessingOptions.vue", "vue", 0, 0, 0, 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 59, 495
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/ProcessingStatsPanel.vue", "vue", 0, 0, 0, 588, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 95, 687
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskControls.vue", "vue", 0, 0, 0, 382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 59, 443
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskListItem.vue", "vue", 0, 0, 0, 267, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 311
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskProgress.vue", "vue", 0, 0, 0, 463, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 71, 541
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskResultPanel.vue", "vue", 0, 0, 0, 672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 101, 787
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/_media-common.scss", "SCSS", 0, 359, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 72, 451
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/useMediaProcessor.ts", "TypeScript", 185, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 31, 231
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/note-monitor/AddNoteTaskDialog.vue", "vue", 0, 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 131
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/note-monitor/EditNoteTaskDialog.vue", "vue", 0, 0, 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 136
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/note-monitor/NoteTaskList.vue", "vue", 0, 0, 0, 346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 45, 398
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/composables/useMediaEvents.ts", "TypeScript", 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 23
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/main.ts", "TypeScript", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 26
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/router/index.ts", "TypeScript", 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 60
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/index.ts", "TypeScript", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 19
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-main.ts", "TypeScript", 232, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 46, 312
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-settings.ts", "TypeScript", 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 35, 237
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-stats.ts", "TypeScript", 183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 43, 258
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-tasks.ts", "TypeScript", 328, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 81, 448
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/plugins/persistence.ts", "TypeScript", 225, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 39, 290
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/style.css", "CSS", 0, 0, 0, 0, 70, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 80
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/media-event-bus.ts", "TypeScript", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 19
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/media-event-manager.ts", "TypeScript", 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 23, 143
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/operation-queue.ts", "TypeScript", 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 34
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/stats-event-bus.ts", "TypeScript", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 43
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Batch.vue", "vue", 0, 0, 0, 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 67, 514
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/BloggerMonitor.vue", "vue", 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 18, 115
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Check.vue", "vue", 0, 0, 0, 262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 43, 307
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Home.vue", "vue", 0, 0, 0, 560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 94, 666
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Media.vue", "vue", 0, 0, 0, 577, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 96, 680
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Monitor.vue", "vue", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 29
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Note.vue", "vue", 0, 0, 0, 632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 108, 748
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/NoteMonitor.vue", "vue", 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 16, 123
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/NoteMonitorData.vue", "vue", 0, 0, 0, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 33, 308
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/UserCheck.vue", "vue", 0, 0, 0, 385, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 68, 456
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/vite-env.d.ts", "TypeScript", 212, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 31, 262
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/stealth.min.js", "JavaScript", 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 6, 0, 7
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/tsconfig.app.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 0, 0, 0, 1, 0, 24
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/tsconfig.json", "JSON with Comments", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/tsconfig.node.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 0, 0, 0, 3, 0, 38
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/vite.config.ts", "TypeScript", 70, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 80
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/vscode/.debug.script.mjs", "JavaScript", 0, 0, 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 32, 18, 128
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/vscode/launch.json", "JSON with Comments", 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 6, 0, 59
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/优化.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 8, 20
"/Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/未使用组件和代码分析.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 14, 41
"Total", "-", 9025, 646, 15, 9965, 70, 60, 124, 6765, 53, 8889, 135, 379, 307, 2078, 4948, 43459