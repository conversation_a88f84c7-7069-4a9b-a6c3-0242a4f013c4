{"file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/router/index.ts": {"language": "TypeScript", "code": 56, "comment": 0, "blank": 4}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/styles/index.scss": {"language": "SCSS", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/logo.svg": {"language": "XML", "code": 13, "comment": 6, "blank": 5}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Media.vue": {"language": "vue", "code": 577, "comment": 7, "blank": 96}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/styles/global.scss": {"language": "SCSS", "code": 286, "comment": 37, "blank": 60}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/BloggerMonitor.vue": {"language": "vue", "code": 94, "comment": 3, "blank": 18}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/assets/vue.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Home.vue": {"language": "vue", "code": 560, "comment": 12, "blank": 94}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/NoteMonitorData.vue": {"language": "vue", "code": 272, "comment": 3, "blank": 33}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/UserCheck.vue": {"language": "vue", "code": 385, "comment": 3, "blank": 68}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Note.vue": {"language": "vue", "code": 632, "comment": 8, "blank": 108}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Batch.vue": {"language": "vue", "code": 443, "comment": 4, "blank": 67}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Check.vue": {"language": "vue", "code": 262, "comment": 2, "blank": 43}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/VideoConverter.vue": {"language": "vue", "code": 35, "comment": 1, "blank": 5}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/style.css": {"language": "CSS", "code": 70, "comment": 0, "blank": 10}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/vite-env.d.ts": {"language": "TypeScript", "code": 212, "comment": 19, "blank": 31}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/ImageProcessor.vue": {"language": "vue", "code": 143, "comment": 2, "blank": 17}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskControls.vue": {"language": "vue", "code": 382, "comment": 2, "blank": 59}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskListItem.vue": {"language": "vue", "code": 267, "comment": 0, "blank": 44}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/BaseMediaProcessor.vue": {"language": "vue", "code": 105, "comment": 0, "blank": 20}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/FileUploader.vue": {"language": "vue", "code": 264, "comment": 1, "blank": 44}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/BaseMediaProcessor.vue": {"language": "vue", "code": 290, "comment": 9, "blank": 42}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/MediaStatsCard.vue": {"language": "vue", "code": 189, "comment": 0, "blank": 28}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/ProcessingOptions.vue": {"language": "vue", "code": 428, "comment": 8, "blank": 59}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/GlobalTaskList.vue": {"language": "vue", "code": 275, "comment": 0, "blank": 41}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/OutputDirectorySelector.vue": {"language": "vue", "code": 212, "comment": 1, "blank": 40}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskProgress.vue": {"language": "vue", "code": 463, "comment": 7, "blank": 71}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/useMediaProcessor.ts": {"language": "TypeScript", "code": 185, "comment": 15, "blank": 31}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/TaskResultPanel.vue": {"language": "vue", "code": 672, "comment": 14, "blank": 101}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/_media-common.scss": {"language": "SCSS", "code": 359, "comment": 20, "blank": 72}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/shared/ProcessingStatsPanel.vue": {"language": "vue", "code": 588, "comment": 4, "blank": 95}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/media/ASRProcessor.vue": {"language": "vue", "code": 93, "comment": 2, "blank": 11}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/blogger-monitor/BloggerWorksList.vue": {"language": "vue", "code": 427, "comment": 15, "blank": 63}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/blogger-monitor/AddBloggerTaskDialog.vue": {"language": "vue", "code": 243, "comment": 1, "blank": 32}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/blogger-monitor/BloggerTaskList.vue": {"language": "vue", "code": 486, "comment": 12, "blank": 66}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/ProgressBar.vue": {"language": "vue", "code": 65, "comment": 0, "blank": 10}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/stats-event-bus.ts": {"language": "TypeScript", "code": 32, "comment": 3, "blank": 8}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/note-monitor/NoteTaskList.vue": {"language": "vue", "code": 346, "comment": 7, "blank": 45}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/note-monitor/EditNoteTaskDialog.vue": {"language": "vue", "code": 122, "comment": 0, "blank": 14}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/media-event-bus.ts": {"language": "TypeScript", "code": 12, "comment": 3, "blank": 4}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/operation-queue.ts": {"language": "TypeScript", "code": 28, "comment": 0, "blank": 6}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/components/note-monitor/AddNoteTaskDialog.vue": {"language": "vue", "code": 115, "comment": 0, "blank": 16}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/utils/media-event-manager.ts": {"language": "TypeScript", "code": 94, "comment": 26, "blank": 23}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-main.ts": {"language": "TypeScript", "code": 232, "comment": 34, "blank": 46}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/index.ts": {"language": "TypeScript", "code": 10, "comment": 4, "blank": 5}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-tasks.ts": {"language": "TypeScript", "code": 328, "comment": 39, "blank": 81}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/main.ts": {"language": "TypeScript", "code": 17, "comment": 4, "blank": 5}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-settings.ts": {"language": "TypeScript", "code": 181, "comment": 21, "blank": 35}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/media-stats.ts": {"language": "TypeScript", "code": 183, "comment": 32, "blank": 43}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/stores/plugins/persistence.ts": {"language": "TypeScript", "code": 225, "comment": 26, "blank": 39}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/App.vue": {"language": "vue", "code": 400, "comment": 15, "blank": 67}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/vscode/launch.json": {"language": "JSON with Comments", "code": 53, "comment": 6, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/composables/useMediaEvents.ts": {"language": "TypeScript", "code": 14, "comment": 7, "blank": 2}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/vscode/.debug.script.mjs": {"language": "JavaScript", "code": 78, "comment": 32, "blank": 18}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/store/monitor-store.ts": {"language": "TypeScript", "code": 261, "comment": 69, "blank": 34}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/index.ts": {"language": "TypeScript", "code": 198, "comment": 37, "blank": 26}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/preload/index.ts": {"language": "TypeScript", "code": 165, "comment": 37, "blank": 20}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/appState.ts": {"language": "TypeScript", "code": 8, "comment": 0, "blank": 2}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/updater.ts": {"language": "TypeScript", "code": 177, "comment": 35, "blank": 31}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/excel.ts": {"language": "TypeScript", "code": 19, "comment": 0, "blank": 3}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/monitor.ts": {"language": "TypeScript", "code": 47, "comment": 1, "blank": 12}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/NoteMonitor.vue": {"language": "vue", "code": 104, "comment": 3, "blank": 16}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/src/views/Monitor.vue": {"language": "vue", "code": 26, "comment": 0, "blank": 3}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/fileSystem.ts": {"language": "TypeScript", "code": 53, "comment": 2, "blank": 8}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/media.ts": {"language": "TypeScript", "code": 35, "comment": 11, "blank": 7}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/updater.ts": {"language": "TypeScript", "code": 21, "comment": 0, "blank": 3}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/xhs.ts": {"language": "TypeScript", "code": 124, "comment": 1, "blank": 13}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/app.ts": {"language": "TypeScript", "code": 67, "comment": 8, "blank": 10}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipc/media-handlers.ts": {"language": "TypeScript", "code": 341, "comment": 46, "blank": 64}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/ipcHandlers.ts": {"language": "TypeScript", "code": 41, "comment": 9, "blank": 9}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/excel-worker.ts": {"language": "TypeScript", "code": 56, "comment": 22, "blank": 10}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/media-worker.ts": {"language": "TypeScript", "code": 369, "comment": 101, "blank": 92}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/code-utils.ts": {"language": "TypeScript", "code": 128, "comment": 1, "blank": 9}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/windows.ts": {"language": "TypeScript", "code": 249, "comment": 37, "blank": 43}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/wdj-proxy.ts": {"language": "TypeScript", "code": 35, "comment": 19, "blank": 12}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/url-utils.ts": {"language": "TypeScript", "code": 69, "comment": 28, "blank": 15}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/img-utils.ts": {"language": "TypeScript", "code": 179, "comment": 33, "blank": 31}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/sleep.ts": {"language": "TypeScript", "code": 16, "comment": 9, "blank": 3}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/number-utils.ts": {"language": "TypeScript", "code": 41, "comment": 20, "blank": 12}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/vite.config.ts": {"language": "TypeScript", "code": 70, "comment": 7, "blank": 3}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/transform-utils.ts": {"language": "TypeScript", "code": 165, "comment": 52, "blank": 23}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/tsconfig.json": {"language": "JSON with Comments", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/header-utils.ts": {"language": "TypeScript", "code": 50, "comment": 1, "blank": 8}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/functional-migration-plan.md": {"language": "<PERSON><PERSON>", "code": 61, "comment": 0, "blank": 18}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron-builder.yml": {"language": "YAML", "code": 53, "comment": 12, "blank": 12}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/BaseASR.ts": {"language": "TypeScript", "code": 156, "comment": 39, "blank": 34}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/package.json": {"language": "JSON", "code": 55, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/data_monitor_design.md": {"language": "<PERSON><PERSON>", "code": 245, "comment": 0, "blank": 33}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/object-utils.ts": {"language": "TypeScript", "code": 46, "comment": 30, "blank": 17}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/package-lock.json": {"language": "JSON", "code": 8483, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/ASRData.ts": {"language": "TypeScript", "code": 115, "comment": 37, "blank": 20}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/ASRDataSeg.ts": {"language": "TypeScript", "code": 38, "comment": 19, "blank": 8}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 3}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/libs/code.d.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/utils/asr/BcutASR.ts": {"language": "TypeScript", "code": 265, "comment": 36, "blank": 56}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/CLAUDE.local.md": {"language": "<PERSON><PERSON>", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/libs/code.js": {"language": "JavaScript", "code": 43, "comment": 0, "blank": 9}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/%E4%BC%98%E5%8C%96.md": {"language": "<PERSON><PERSON>", "code": 12, "comment": 0, "blank": 8}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/stealth.min.js": {"language": "JavaScript", "code": 1, "comment": 6, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/.claude/settings.local.json": {"language": "JSON", "code": 15, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%8D%9A%E4%B8%BB%E7%9B%91%E6%8E%A7%E7%95%8C%E9%9D%A2%E4%BC%98%E5%8C%96%E6%94%B9%E9%80%A0.md": {"language": "<PERSON><PERSON>", "code": 30, "comment": 0, "blank": 6}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E7%88%86%E6%96%87%E6%8B%86%E8%A7%A3%E5%8A%9F%E8%83%BD.md": {"language": "<PERSON><PERSON>", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/storage-service.ts": {"language": "TypeScript", "code": 86, "comment": 3, "blank": 14}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E9%A1%B9%E7%9B%AE%E6%9E%B6%E6%9E%84%E4%B8%8E%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97%E6%80%BB%E7%BB%93.md": {"language": "<PERSON><PERSON>", "code": 92, "comment": 0, "blank": 33}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron_xhs_user_notes_no_puppeteer.md": {"language": "<PERSON><PERSON>", "code": 96, "comment": 0, "blank": 23}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%8D%9A%E4%B8%BB%E5%88%86%E6%9E%90%E6%A8%A1%E5%9D%97.md": {"language": "<PERSON><PERSON>", "code": 8, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/base-api.ts": {"language": "TypeScript", "code": 252, "comment": 60, "blank": 42}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E6%96%B0%E6%A8%A1%E5%9D%97%E8%AE%BE%E8%AE%A1%E6%96%B9%E6%A1%88.md": {"language": "<PERSON><PERSON>", "code": 582, "comment": 0, "blank": 83}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86%E9%97%AE%E9%A2%98%E4%BF%AE%E5%A4%8D%E6%8A%A5%E5%91%8A.md": {"language": "<PERSON><PERSON>", "code": 123, "comment": 0, "blank": 28}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E6%A8%A1%E5%9D%97%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86%E4%BC%98%E5%8C%96%E6%96%B9%E6%A1%88.md": {"language": "<PERSON><PERSON>", "code": 555, "comment": 0, "blank": 105}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%8D%9A%E4%B8%BB%E7%9B%91%E6%8E%A7%E6%A8%A1%E5%9D%97%E5%AE%8C%E5%96%84%E7%89%88%E8%A7%84%E5%88%92.md": {"language": "<PERSON><PERSON>", "code": 489, "comment": 0, "blank": 130}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E6%A8%A1%E5%9D%97%E5%88%86%E6%9E%90%E6%8A%A5%E5%91%8A.md": {"language": "<PERSON><PERSON>", "code": 165, "comment": 0, "blank": 58}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/blogger-monitor-task.ts": {"language": "TypeScript", "code": 223, "comment": 24, "blank": 37}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E7%BC%96%E7%A0%81%E4%BF%AE%E6%94%B9%E5%AE%8C%E6%88%90%E6%80%BB%E7%BB%93.md": {"language": "<PERSON><PERSON>", "code": 124, "comment": 0, "blank": 27}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E6%A8%A1%E5%9D%97%E9%9B%86%E6%88%90%E5%AE%8C%E6%88%90%E6%8A%A5%E5%91%8A.md": {"language": "<PERSON><PERSON>", "code": 166, "comment": 0, "blank": 49}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/xhs-worker.ts": {"language": "TypeScript", "code": 165, "comment": 97, "blank": 39}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E8%A7%86%E9%A2%91%E5%A4%84%E7%90%86%E7%BB%84%E4%BB%B6%E5%90%8E%E7%AB%AF%E8%BD%AC%E6%8D%A2%E6%96%B9%E6%B3%95%E5%AE%8C%E5%96%84%E6%96%B9%E6%A1%88.md": {"language": "<PERSON><PERSON>", "code": 394, "comment": 0, "blank": 105}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E5%AA%92%E4%BD%93%E6%A8%A1%E5%9D%97%E9%87%8D%E6%9E%84%E5%AE%9E%E6%96%BD%E6%96%B9%E6%A1%88.md": {"language": "<PERSON><PERSON>", "code": 1058, "comment": 0, "blank": 198}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E5%A4%9A%E5%AA%92%E4%BD%93%E5%B7%A5%E5%85%B7%EF%BC%88%E6%96%B0%E6%A8%A1%E5%9D%97%EF%BC%89/%E9%97%AE%E9%A2%98%E4%BF%AE%E5%A4%8D/%E7%9B%AE%E5%89%8D%E5%AD%98%E5%9C%A8%E7%9A%84%E9%97%AE%E9%A2%98.md": {"language": "<PERSON><PERSON>", "code": 18, "comment": 0, "blank": 8}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/docs/%E6%8B%86%E8%A7%A3%E6%95%B0%E6%8D%AE.md": {"language": "<PERSON><PERSON>", "code": 155, "comment": 0, "blank": 29}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/ffmpeg-service.ts": {"language": "TypeScript", "code": 412, "comment": 109, "blank": 88}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/public/vite.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/xhs-word-api.ts": {"language": "TypeScript", "code": 30, "comment": 2, "blank": 10}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/asr-service.ts": {"language": "TypeScript", "code": 272, "comment": 61, "blank": 61}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/public/html/biaoqing.html": {"language": "HTML", "code": 122, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-arm64/versions.json": {"language": "JSON", "code": 30, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/monitor-service.ts": {"language": "TypeScript", "code": 288, "comment": 25, "blank": 36}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-arm64/README.md": {"language": "<PERSON><PERSON>", "code": 40, "comment": 0, "blank": 7}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-darwin-arm64/package.json": {"language": "JSON", "code": 40, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-darwin-arm64/README.md": {"language": "<PERSON><PERSON>", "code": 13, "comment": 0, "blank": 6}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/config-manager.ts": {"language": "TypeScript", "code": 204, "comment": 86, "blank": 55}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-darwin-x64/package.json": {"language": "JSON", "code": 40, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-darwin-x64/package-lock.json": {"language": "JSON", "code": 44, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-arm64/package.json": {"language": "JSON", "code": 36, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-x64/package.json": {"language": "JSON", "code": 36, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-x64/package-lock.json": {"language": "JSON", "code": 22, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-x64/versions.json": {"language": "JSON", "code": 30, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/cache-manager.ts": {"language": "TypeScript", "code": 153, "comment": 45, "blank": 38}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/services/note-monitor-task.ts": {"language": "TypeScript", "code": 186, "comment": 21, "blank": 29}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/xhs-api.ts": {"language": "TypeScript", "code": 86, "comment": 10, "blank": 22}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/web-xhs-api.ts": {"language": "TypeScript", "code": 380, "comment": 56, "blank": 62}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/error-types.ts": {"language": "TypeScript", "code": 132, "comment": 44, "blank": 20}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/tsconfig.app.json": {"language": "JSON", "code": 23, "comment": 1, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/media-module-refactor-design.md": {"language": "<PERSON><PERSON>", "code": 1411, "comment": 0, "blank": 231}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/config.gypi": {"language": "Python", "code": 379, "comment": 1, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/xhs-spider.ts": {"language": "TypeScript", "code": 210, "comment": 103, "blank": 33}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/monitor-refactor-plan.md": {"language": "<PERSON><PERSON>", "code": 335, "comment": 0, "blank": 113}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/unified-types.ts": {"language": "TypeScript", "code": 131, "comment": 23, "blank": 22}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/logs.md": {"language": "<PERSON><PERSON>", "code": 135, "comment": 0, "blank": 59}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/ipc_refactoring_plan.md": {"language": "<PERSON><PERSON>", "code": 55, "comment": 0, "blank": 11}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/type-mappings.ts": {"language": "TypeScript", "code": 149, "comment": 25, "blank": 10}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/api-types.ts": {"language": "TypeScript", "code": 93, "comment": 4, "blank": 7}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/web-types.ts": {"language": "TypeScript", "code": 37, "comment": 0, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/index.html": {"language": "HTML", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/tsconfig.node.json": {"language": "JSON", "code": 35, "comment": 3, "blank": 0}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-x64/lib/index.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/electron/main/api/models/note.ts": {"language": "TypeScript", "code": 122, "comment": 2, "blank": 12}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-x64/lib/glib-2.0/include/glibconfig.h": {"language": "C++", "code": 154, "comment": 14, "blank": 54}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-arm64/lib/index.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/api-analysis-report.md": {"language": "<PERSON><PERSON>", "code": 231, "comment": 0, "blank": 63}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/build/sharp/%40img/sharp-libvips-darwin-arm64/lib/glib-2.0/include/glibconfig.h": {"language": "C++", "code": 153, "comment": 14, "blank": 54}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/%E6%9C%AA%E4%BD%BF%E7%94%A8%E7%BB%84%E4%BB%B6%E5%92%8C%E4%BB%A3%E7%A0%81%E5%88%86%E6%9E%90.md": {"language": "<PERSON><PERSON>", "code": 27, "comment": 0, "blank": 14}, "file:///Users/<USER>/kolcm/kol-xhs-app-tool/xhs-pc-app/project_summary.md": {"language": "<PERSON><PERSON>", "code": 142, "comment": 0, "blank": 13}}