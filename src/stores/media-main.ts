import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useMediaTasksStore } from './media-tasks'
import { useMediaSettingsStore } from './media-settings'
import { useMediaEvents } from '@/composables/useMediaEvents'
import { emitTaskFailed } from '@/utils/stats-event-bus'
import type { ProcessingOptions } from './media-tasks'

interface FFmpegStatus {
  available: boolean
  version?: string
  path?: string
  error?: string
}

/**
 * 主媒体 Store
 * 协调和统一管理所有媒体相关的子 stores
 */
export const useMediaMainStore = defineStore('media-main', () => {
  // 子 stores
  const tasksStore = useMediaTasksStore()
  const settingsStore = useMediaSettingsStore()
  const { mediaEventBus } = useMediaEvents()


  // 初始化状态
  const isInitialized = ref(false)
  const isProcessing = ref(false)

  // FFmpeg 状态
  const ffmpegStatus = ref<FFmpegStatus>({
    available: false,
    version: undefined,
    path: undefined,
    error: undefined
  })

  // 活动任务追踪
  const activeTaskIds = ref<Set<string>>(new Set())

  // 初始化
  const initialize = async (): Promise<void> => {
    if (isInitialized.value) return

    try {
      console.log('[MediaMainStore] 开始初始化...')

      // 检查 FFmpeg 状态
      const ffmpegResponse = await window.electronAPI.media.checkFFmpegStatus()
      if (ffmpegResponse.success && ffmpegResponse.data) {
        ffmpegStatus.value = ffmpegResponse.data
        if (!ffmpegStatus.value.available) {
          console.error('[MediaMainStore] FFmpeg 不可用:', ffmpegStatus.value.error)
        }
      }

      isInitialized.value = true
      console.log('[MediaMainStore] 初始化完成')
    } catch (error) {
      console.error('[MediaMainStore] 初始化失败:', error)
      throw error
    }
  }

  // 开始单个任务
  const startSingleTask = async (taskId: string): Promise<void> => {
    try {
      const task = tasksStore.singleTasks.get(taskId)
      if (!task) {
        throw new Error(`任务不存在: ${taskId}`)
      }

      if (task.status === 'processing') {
        console.warn(`任务已在处理中: ${taskId}`)
        return
      }

      // 检查并发限制
      if (activeTaskIds.value.size >= settingsStore.settings.maxConcurrentTasks) {
        console.warn(`已达到最大并发任务数，任务 ${taskId} 将等待`)
        // 不抛出错误，而是等待一段时间后重试
        setTimeout(() => startSingleTask(taskId), 1000)
        return
      }

      // 标记任务为处理中
      tasksStore.updateTaskStatus(taskId, 'processing', undefined, undefined, 'internal')
      activeTaskIds.value.add(taskId)

      let response: any
      const options = JSON.parse(JSON.stringify(task.options))

      switch (task.type) {
        case 'video-convert':
          response = await window.electronAPI.media.convertVideo(
            task.filePath,
            task.outputPath,
            options,
            taskId
          )
          break

        case 'audio-extract':
          response = await window.electronAPI.media.extractAudio(
            task.filePath,
            task.outputPath,
            options,
            taskId
          )
          break

        case 'asr':
          response = await window.electronAPI.media.extractText(
            task.filePath,
            options,
            taskId
          )
          break

        case 'image-process':
          response = await window.electronAPI.media.processImages(
            [task.filePath],
            options,
            taskId
          )
          break

        default:
          throw new Error(`不支持的任务类型: ${task.type}`)
      }

      if (!response.success) {
        throw new Error(response.error || '任务处理失败')
      }

      // 任务完成 - 创建任务结果
      const taskResult = {
        outputPath: response.data?.outputPath || task.outputPath,
        fileUid: response.data?.fileUid || task.fileUid,
        processingTime: response.data?.processingTime || 0,
        ...response.data
      }

      // 更新任务状态
      tasksStore.updateTaskStatus(taskId, 'completed', 100, undefined, 'internal')

      // 发送任务完成事件，触发TaskResult创建
      mediaEventBus.emit('task:completed', { taskId, result: taskResult })

      console.log(`[MediaMainStore] 任务完成: ${taskId}`, taskResult)

    } catch (error: any) {
      console.error(`[MediaMainStore] 任务失败 ${taskId}:`, error)
      tasksStore.updateTaskStatus(taskId, 'error', undefined, error.message, 'internal')

      // 触发统计更新事件
      const task = tasksStore.singleTasks.get(taskId)
      if (task) {
        emitTaskFailed(taskId, task.type)
      }
    } finally {
      activeTaskIds.value.delete(taskId)
    }
  }


  // 暂停任务
  const pauseSingleTask = async (taskId: string): Promise<void> => {
    try {
      const response = await window.electronAPI.media.pauseTask(taskId)
      if (response.success) {
        tasksStore.updateTaskStatus(taskId, 'paused', undefined, undefined, 'internal')
        activeTaskIds.value.delete(taskId)
      }
    } catch (error) {
      console.error(`[MediaMainStore] 暂停任务失败 ${taskId}:`, error)
    }
  }

  // 恢复任务
  const resumeSingleTask = async (taskId: string): Promise<void> => {
    try {
      const response = await window.electronAPI.media.resumeTask(taskId)
      if (response.success) {
        tasksStore.updateTaskStatus(taskId, 'processing', undefined, undefined, 'internal')
        activeTaskIds.value.add(taskId)
      }
    } catch (error) {
      console.error(`[MediaMainStore] 恢复任务失败 ${taskId}:`, error)
    }
  }

  // 取消任务
  const cancelSingleTask = async (taskId: string): Promise<void> => {
    try {
      const response = await window.electronAPI.media.cancelTask(taskId)
      if (response.success) {
        tasksStore.updateTaskStatus(taskId, 'error', undefined, '用户取消', 'internal')
        activeTaskIds.value.delete(taskId)
      }
    } catch (error) {
      console.error(`[MediaMainStore] 取消任务失败 ${taskId}:`, error)
    }
  }

  // 处理文件
  const processFiles = async (
    taskType: string,
    files: any[],
    options: ProcessingOptions,
    outputDirectory: string
  ): Promise<void> => {
    if (files.length === 0) return

    try {
      // 创建单个任务
      const taskIds = await tasksStore.createTasksFromFiles(
        taskType,
        files,
        options,
        outputDirectory
      )

      // 并行启动任务（但受并发限制控制）
      const startPromises = taskIds.map(taskId =>
        startSingleTask(taskId).catch(error => {
          console.error(`启动任务失败 ${taskId}:`, error)
        })
      )

      // 等待所有任务启动完成（不等待执行完成）
      await Promise.allSettled(startPromises)
    } catch (error) {
      console.error('[MediaMainStore] processFiles 失败:', error)
      throw error
    }
  }

  // 清理临时文件
  const cleanupTempFiles = async (): Promise<void> => {
    const response = await window.electronAPI.media.cleanupTemp()
    if (!response.success) {
      console.error('[MediaMainStore] 清理临时文件失败:', response.error)
    }
  }

  // 自动清理已完成任务
  const startAutoCleanup = () => {
    if (!settingsStore.settings.autoCleanupCompleted) return

    const cleanupInterval = settingsStore.settings.cleanupAfterHours * 60 * 60 * 1000

    setInterval(() => {
      const now = Date.now()
      const cutoffTime = now - cleanupInterval

      // 清理单个任务
      for (const [taskId, task] of tasksStore.singleTasks) {
        if (
          task.status === 'completed' &&
          task.completedAt &&
          task.completedAt < cutoffTime
        ) {
          tasksStore.removeSingleTask(taskId)
        }
      }

    }, 60 * 60 * 1000) // 每小时检查一次
  }

  // 获取任务类型名称
  const getTaskTypeName = (type: string): string => {
    const typeNames: Record<string, string> = {
      'video-convert': '视频转换',
      'audio-extract': '音频提取',
      'asr': '语音识别',
      'image-process': '图片处理'
    }
    return typeNames[type] || type
  }

  // 监听设置更新
  mediaEventBus.on('settings:update', () => {
    // 如果启用了自动清理，启动清理定时器
    if (settingsStore.settings.autoCleanupCompleted) {
      startAutoCleanup()
    }
  })

  return {
    // 状态
    isInitialized,
    isProcessing,
    ffmpegStatus,
    activeTaskIds: computed(() => activeTaskIds.value),

    // 子 store 引用
    tasksStore: computed(() => tasksStore),
    settingsStore: computed(() => settingsStore),

    // 方法
    initialize,
    startSingleTask,
    pauseSingleTask,
    resumeSingleTask,
    cancelSingleTask,
    processFiles,
    cleanupTempFiles,
    getTaskTypeName
  }
})