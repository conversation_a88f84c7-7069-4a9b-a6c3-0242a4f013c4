<template>
  <div class="processing-stats-panel">
    <div class="stats-header">
      <h4>
        <Icon icon="mdi:chart-line" class="header-icon" />
        处理统计
      </h4>
      <div class="header-actions">
        <el-button size="small" @click="refreshStats" :loading="isRefreshing">
          <Icon icon="mdi:refresh" />
          刷新
        </el-button>
        <el-button size="small" @click="clearStats" v-if="showClearButton">
          <Icon icon="mdi:trash-can-outline" />
          清空
        </el-button>
      </div>
    </div>

    <div class="stats-content" v-if="stats">
      <!-- 总体统计 -->
      <div class="stats-overview">
        <div class="stat-card">
          <Icon icon="mdi:check-circle" class="stat-icon success" />
          <div class="stat-info">
            <div class="stat-value">{{ stats.totalProcessed }}</div>
            <div class="stat-label">已处理任务</div>
          </div>
        </div>

        <div class="stat-card">
          <Icon icon="mdi:file-multiple" class="stat-icon info" />
          <div class="stat-info">
            <div class="stat-value">{{ formatFileSize(stats.totalSize) }}</div>
            <div class="stat-label">处理大小</div>
          </div>
        </div>

        <div class="stat-card">
          <Icon icon="mdi:clock-outline" class="stat-icon warning" />
          <div class="stat-info">
            <div class="stat-value">{{ formatTime(stats.totalTime) }}</div>
            <div class="stat-label">总耗时</div>
          </div>
        </div>

        <div class="stat-card">
          <Icon icon="mdi:percent" class="stat-icon primary" />
          <div class="stat-info">
            <div class="stat-value">{{ stats.successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </div>

      <!-- 详细统计 -->
      <div class="stats-details" v-if="showDetails">
        <el-divider>详细统计</el-divider>

        <div class="stats-section">
          <h5>按类型统计</h5>
          <div class="stats-grid">
            <div v-for="(typeStats, type) in stats.tasksByType" :key="type" class="stat-item type-stat-item">
              <div class="type-header">
                <Icon :icon="getTypeIcon(type)" class="type-icon" />
                <span class="type-name">{{ getTypeName(type) }}</span>
                <span class="type-total">{{ typeStats.total }}</span>
              </div>
              <div class="type-details">
                <span class="success-count">
                  <Icon icon="mdi:check-circle" class="success-icon" />
                  成功: {{ typeStats.success }}
                </span>
                <span class="failed-count">
                  <Icon icon="mdi:close-circle" class="failed-icon" />
                  失败: {{ typeStats.failed }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="stats-section" v-if="stats.averageProcessingTime > 0">
          <h5>性能指标</h5>
          <div class="performance-metrics">
            <div class="metric-item">
              <span class="metric-label">平均处理时间:</span>
              <span class="metric-value">{{ formatTime(stats.averageProcessingTime) }}</span>
            </div>
            <div class="metric-item" v-if="estimatedThroughput > 0">
              <span class="metric-label">预估吞吐量:</span>
              <span class="metric-value">{{ estimatedThroughput }} 任务/小时</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-stats" v-if="isEmpty">
        <Icon icon="mdi:chart-line-variant" class="empty-icon" />
        <p class="empty-text">{{ emptyText }}</p>
        <p class="empty-hint">{{ emptyHint }}</p>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-stats" v-if="isLoading">
      <el-skeleton animated>
        <template #template>
          <div class="stats-overview">
            <div class="stat-card" v-for="i in 4" :key="i">
              <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
              <div class="stat-info">
                <el-skeleton-item variant="text" style="width: 60px;" />
                <el-skeleton-item variant="text" style="width: 80px;" />
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'
import { useMediaTasksStore } from '@/stores'
import { statsEventBus } from '@/utils/stats-event-bus'
import { useMediaStatsStore } from '@/stores/media-stats'
import type { ProcessingStats } from '@/stores/media-stats'

// Props
interface Props {
  showDetails?: boolean
  showClearButton?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
  emptyText?: string
  emptyHint?: string
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: true,
  showClearButton: true,
  autoRefresh: false,
  refreshInterval: 30000, // 30秒
  emptyText: '暂无统计数据',
  emptyHint: '开始处理任务后统计信息将显示在这里'
})

// Emits
const emit = defineEmits<{
  statsUpdated: [stats: ProcessingStats]
  statsCleared: []
}>()

// 状态
const tasksStore = useMediaTasksStore()
const statsStore = useMediaStatsStore()
const isLoading = ref(false)
const isRefreshing = ref(false)
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const stats = computed(() => statsStore.stats)

const isEmpty = computed(() => {
  return !stats.value || stats.value.totalProcessed === 0
})

const estimatedThroughput = computed(() => {
  if (!stats.value || stats.value.averageProcessingTime === 0) return 0
  return Math.round(3600 / stats.value.averageProcessingTime)
})

// 监听统计数据变化
watch(stats, (newStats) => {
  if (newStats) {
    emit('statsUpdated', newStats)
  }
}, { deep: true })

// 事件处理器
const handleStatsUpdate = () => {
  console.log('[ProcessingStatsPanel] 收到统计更新事件')
  refreshStats()
}

const handleTaskCompleted = (data: { taskId: string, taskType: string }) => {
  console.log(`[ProcessingStatsPanel] 任务完成: ${data.taskId} (${data.taskType})`)
  refreshStats()
}

const handleTaskFailed = (data: { taskId: string, taskType: string }) => {
  console.log(`[ProcessingStatsPanel] 任务失败: ${data.taskId} (${data.taskType})`)
  refreshStats()
}

// 方法
const refreshStats = async () => {
  isRefreshing.value = true
  try {
    // 获取当前任务和结果数据
    const tasks = tasksStore.allTasks || []
    const results = Array.from(tasksStore.taskResults.values() || [])

    console.log(`[ProcessingStatsPanel] 刷新统计: 任务数=${tasks.length}, 结果数=${results.length}`)
    console.log(`[ProcessingStatsPanel] 任务详情:`, tasks.map(t => ({ id: t.id, status: t.status, fileName: t.fileName })))
    console.log(`[ProcessingStatsPanel] 结果详情:`, results.map(r => ({ id: r.id, fileName: r.fileName, success: r.success })))

    // 刷新统计数据
    await statsStore.refreshStats(tasks, results)

    console.log(`[ProcessingStatsPanel] 统计数据更新完成, 当前stats:`, stats.value)
  } catch (error: any) {
    console.error('[ProcessingStatsPanel] 刷新统计失败:', error)
    ElMessage.error(`刷新统计失败: ${error.message}`)
  } finally {
    isRefreshing.value = false
  }
}

const clearStats = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有统计数据吗？此操作不可撤销。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const success = await statsStore.clearStats()

    if (success) {
      emit('statsCleared')
      ElMessage.success('统计数据已清空')
    } else {
      ElMessage.error('清空统计数据失败')
    }
  } catch (error) {
    // 用户取消或发生错误
    if (error !== 'cancel') {
      console.error('清空统计数据时发生错误:', error)
    }
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (seconds: number): string => {
  if (seconds === 0) return '0秒'
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours}时${minutes}分${secs}秒`
}

const getTypeIcon = (type: string): string => {
  const icons = {
    'video-convert': 'mdi:video-outline',
    'audio-extract': 'mdi:music-note',
    'asr': 'mdi:microphone',
    'image-process': 'mdi:image-outline'
  }
  return icons[type] || 'mdi:file-outline'
}

const getTypeName = (type: string): string => {
  const names = {
    'video-convert': '视频转换',
    'audio-extract': '音频提取',
    'asr': '语音识别',
    'image-process': '图片处理'
  }
  return names[type] || type
}

const getStatusIcon = (status: string): string => {
  const icons = {
    pending: 'mdi:clock-outline',
    processing: 'mdi:play-circle',
    completed: 'mdi:check-circle',
    error: 'mdi:alert-circle',
    paused: 'mdi:pause-circle'
  }
  return icons[status] || 'mdi:help-circle'
}

const getStatusClass = (status: string): string => {
  const classes = {
    pending: 'status-pending',
    processing: 'status-processing',
    completed: 'status-completed',
    error: 'status-error',
    paused: 'status-paused'
  }
  return classes[status] || ''
}

const getStatusName = (status: string): string => {
  const names = {
    pending: '等待中',
    processing: '处理中',
    completed: '已完成',
    error: '失败',
    paused: '已暂停'
  }
  return names[status] || status
}

const startAutoRefresh = () => {
  if (props.autoRefresh && props.refreshInterval > 0) {
    refreshTimer = setInterval(() => {
      refreshStats()
    }, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(async () => {
  isLoading.value = true
  try {
    console.log('[ProcessingStatsPanel] 组件挂载，注册事件监听器')

    // 注册事件监听器
    statsEventBus.on('stats-update', handleStatsUpdate)
    statsEventBus.on('task-completed', handleTaskCompleted)
    statsEventBus.on('task-failed', handleTaskFailed)

    // 初始化统计数据
    await refreshStats()
    startAutoRefresh()
  } finally {
    isLoading.value = false
  }
})

// 监听自动刷新配置变化
watch(() => props.autoRefresh, (newValue) => {
  if (newValue) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 组件卸载时清理定时器和事件监听器
onUnmounted(() => {
  stopAutoRefresh()

  // 清理事件监听器
  statsEventBus.off('stats-update', handleStatsUpdate)
  statsEventBus.off('task-completed', handleTaskCompleted)
  statsEventBus.off('task-failed', handleTaskFailed)

  console.log('[ProcessingStatsPanel] 组件卸载，清理定时器和事件监听器')
})

// 导出方法供父组件使用
defineExpose({
  refreshStats,
  clearStats
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.processing-stats-panel {
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 500;

      .header-icon {
        color: $primary-color;
        font-size: 18px;
      }
    }

    .header-actions {
      display: flex;
      gap: $spacing-small;
    }
  }

  .stats-content {
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: $spacing-base;
      margin-bottom: $spacing-large;

      .stat-card {
        display: flex;
        align-items: center;
        gap: $spacing-base;
        padding: $spacing-base;
        background: $background-light;
        border-radius: $border-radius-base;
        border: 1px solid $border-lighter;

        .stat-icon {
          font-size: 32px;

          &.success {
            color: $success-color;
          }

          &.info {
            color: $info-color;
          }

          &.warning {
            color: $warning-color;
          }

          &.primary {
            color: $primary-color;
          }
        }

        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: $text-primary;
            line-height: 1.2;
          }

          .stat-label {
            font-size: 12px;
            color: $text-secondary;
            margin-top: 2px;
          }
        }
      }
    }

    .stats-details {
      .stats-section {
        margin-bottom: $spacing-large;

        h5 {
          margin: 0 0 $spacing-base 0;
          color: $text-primary;
          font-size: 14px;
          font-weight: 500;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
          gap: $spacing-small;

          .stat-item {
            display: flex;
            align-items: center;
            gap: $spacing-small;
            padding: $spacing-small;
            background: $background-light;
            border-radius: $border-radius-small;

            .type-icon {
              color: $primary-color;
              font-size: 16px;
            }

            .type-name,
            .status-name {
              flex: 1;
              font-size: 12px;
              color: $text-secondary;
            }

            .type-count,
            .status-count {
              font-weight: 500;
              color: $text-primary;
            }

            .status-pending {
              color: $info-color;
            }

            .status-processing {
              color: $primary-color;
            }

            .status-completed {
              color: $success-color;
            }

            .status-error {
              color: $danger-color;
            }

            .status-paused {
              color: $warning-color;
            }
          }

          .type-stat-item {
            flex-direction: column;
            align-items: stretch;
            gap: $spacing-small;

            .type-header {
              display: flex;
              align-items: center;
              gap: $spacing-small;

              .type-icon {
                font-size: 18px;
                color: $primary-color;
              }

              .type-name {
                flex: 1;
                font-weight: 600;
                color: $text-primary;
              }

              .type-total {
                font-weight: 700;
                color: $primary-color;
                font-size: 16px;
              }
            }

            .type-details {
              display: flex;
              justify-content: space-between;
              gap: $spacing-small;
              padding-left: 26px; // 对齐图标

              .success-count,
              .failed-count {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;

                .success-icon {
                  color: $success-color;
                }

                .failed-icon {
                  color: $danger-color;
                }
              }
            }
          }
        }

        .performance-metrics {
          .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: $spacing-small 0;
            border-bottom: 1px solid $border-lighter;

            &:last-child {
              border-bottom: none;
            }

            .metric-label {
              font-size: 12px;
              color: $text-secondary;
            }

            .metric-value {
              font-weight: 500;
              color: $text-primary;
            }
          }
        }
      }
    }

    .empty-stats {
      text-align: center;
      padding: $spacing-large;
      color: $text-secondary;

      .empty-icon {
        font-size: 48px;
        color: $text-placeholder;
        margin-bottom: $spacing-base;
      }

      .empty-text {
        font-size: 16px;
        margin: 0 0 $spacing-small 0;
      }

      .empty-hint {
        font-size: 12px;
        margin: 0;
        color: $text-placeholder;
      }
    }
  }

  .loading-stats {
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: $spacing-base;

      .stat-card {
        display: flex;
        align-items: center;
        gap: $spacing-base;
        padding: $spacing-base;
        background: $background-light;
        border-radius: $border-radius-base;
        border: 1px solid $border-lighter;

        .stat-info {
          flex: 1;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .processing-stats-panel {
    .stats-header {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-small;

      .header-actions {
        justify-content: center;
      }
    }

    .stats-content {
      .stats-overview {
        grid-template-columns: 1fr;
      }

      .stats-details {
        .stats-section {
          .stats-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>