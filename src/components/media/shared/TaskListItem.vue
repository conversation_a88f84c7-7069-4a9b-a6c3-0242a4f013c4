<template>
  <div class="task-list-item" :class="itemClass">
    <div class="task-info">
      <div class="task-icon">
        <Icon :icon="taskIcon" :class="iconClass" />
      </div>

      <div class="task-details">
        <div class="task-name">{{ task.fileName }}</div>
        <div class="task-meta">
          <span class="task-type">{{ getTaskTypeLabel(task.type) }}</span>
          <span class="task-status" :class="`status-${task.status}`">
            {{ getTaskStatusLabel(task.status) }}
          </span>
          <span class="task-time" v-if="task.createdAt">
            {{ formatTime(task.createdAt) }}
          </span>
        </div>
        <div class="task-error" v-if="task.error">
          {{ task.error }}
        </div>
      </div>
    </div>

    <div class="task-progress" v-if="!compact">
      <el-progress :percentage="task.progress" :status="progressStatus" :show-text="false" :stroke-width="6" />
      <div class="progress-text">{{ task.progress }}%</div>
    </div>

    <div class="task-actions" v-if="allowControl">
      <el-button-group size="small">
        <el-button v-if="task.status === 'processing'" @click="$emit('action', 'pause', task.id)"
          :disabled="!allowControl">
          <Icon icon="mdi:pause" />
        </el-button>

        <el-button v-if="task.status === 'paused'" @click="$emit('action', 'resume', task.id)"
          :disabled="!allowControl">
          <Icon icon="mdi:play" />
        </el-button>

        <el-button v-if="task.status === 'error'" @click="$emit('action', 'retry', task.id)" :disabled="!allowControl">
          <Icon icon="mdi:refresh" />
        </el-button>

        <el-button @click="$emit('action', 'remove', task.id)" :disabled="!allowControl" type="danger">
          <Icon icon="mdi:close" />
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

interface Task {
  id: string
  type: string
  fileName: string
  status: string
  progress: number
  error?: string
  createdAt: number
  startTime?: number
  completedAt?: number
}

interface Props {
  task: Task
  allowControl?: boolean
  compact?: boolean
  selectable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  allowControl: true,
  compact: false,
  selectable: false
})

defineEmits<{
  action: [action: string, taskId: string]
}>()

// 计算属性
const itemClass = computed(() => ({
  'compact': props.compact,
  'error': props.task.status === 'error',
  'processing': props.task.status === 'processing',
  'completed': props.task.status === 'completed'
}))

const taskIcon = computed(() => {
  const icons = {
    'video-convert': 'mdi:video-outline',
    'audio-extract': 'mdi:music',
    'asr': 'mdi:microphone',
    'image-process': 'mdi:image-outline',
    'batch': 'mdi:playlist-check'
  }
  return icons[props.task.type as keyof typeof icons] || 'mdi:file'
})

const iconClass = computed(() => ({
  'text-primary': props.task.status === 'processing',
  'text-success': props.task.status === 'completed',
  'text-danger': props.task.status === 'error',
  'text-warning': props.task.status === 'paused',
  'text-info': props.task.status === 'pending'
}))

const progressStatus = computed(() => {
  if (props.task.status === 'error') return 'exception'
  if (props.task.status === 'completed') return 'success'
  return undefined
})

// 工具方法
const getTaskTypeLabel = (type: string): string => {
  const labels = {
    'video-convert': '视频转换',
    'audio-extract': '音频提取',
    'asr': '语音识别',
    'image-process': '图片处理',
    'batch': '批量处理'
  }
  return labels[type as keyof typeof labels] || type
}

const getTaskStatusLabel = (status: string): string => {
  const labels = {
    'pending': '等待中',
    'processing': '处理中',
    'completed': '已完成',
    'error': '失败',
    'paused': '已暂停'
  }
  return labels[status as keyof typeof labels] || status
}

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`

  return date.toLocaleDateString()
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/index.scss' as *;

.task-list-item {
  display: flex;
  align-items: center;
  gap: $spacing-base;
  padding: $spacing-base;
  background: white;
  border: 1px solid $border-light;
  border-radius: $border-radius;
  transition: all 0.3s ease;
  margin-bottom: $spacing-small;

  &:hover {
    border-color: $primary-color;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.error {
    border-left: 4px solid $danger-color;
  }

  &.processing {
    border-left: 4px solid $primary-color;
  }

  &.completed {
    border-left: 4px solid $success-color;
  }

  &.compact {
    padding: $spacing-small;

    .task-details {
      .task-meta {
        font-size: 11px;
      }
    }

    .task-progress {
      min-width: 60px;
    }
  }

  .task-info {
    display: flex;
    align-items: center;
    gap: $spacing-small;
    flex: 1;

    .task-icon {
      font-size: 24px;

      &.text-primary {
        color: $primary-color;
      }

      &.text-success {
        color: $success-color;
      }

      &.text-danger {
        color: $danger-color;
      }

      &.text-warning {
        color: $warning-color;
      }

      &.text-info {
        color: $info-color;
      }
    }

    .task-details {
      flex: 1;

      .task-name {
        font-weight: 500;
        color: $text-primary;
        margin-bottom: 4px;
        word-break: break-all;
      }

      .task-meta {
        display: flex;
        gap: $spacing-small;
        font-size: 12px;
        color: $text-secondary;

        .task-status {
          &.status-pending {
            color: $info-color;
          }

          &.status-processing {
            color: $primary-color;
          }

          &.status-completed {
            color: $success-color;
          }

          &.status-error {
            color: $danger-color;
          }

          &.status-paused {
            color: $warning-color;
          }
        }
      }

      .task-error {
        margin-top: 4px;
        padding: 4px 8px;
        background: mix(white, $danger-color, 95%);
        color: $danger-color;
        border-radius: $border-radius-small;
        font-size: 11px;
        word-break: break-all;
      }
    }
  }

  .task-progress {
    min-width: 120px;
    text-align: center;

    .el-progress {
      margin-bottom: 4px;
    }

    .progress-text {
      font-size: 12px;
      color: $text-secondary;
    }
  }

  .task-actions {
    .el-button-group .el-button {
      margin-left: 0;
    }
  }
}

@media (max-width: 768px) {
  .task-list-item {
    flex-direction: column;
    align-items: stretch;

    .task-info {
      margin-bottom: $spacing-small;
    }

    .task-progress {
      min-width: auto;
      margin-bottom: $spacing-small;
    }

    .task-actions {
      align-self: center;
    }
  }
}
</style>