<template>
  <div class="global-task-list">
    <div v-if="groupByType" class="grouped-tasks">
      <div v-for="(typeTasks, type) in groupedTasks" :key="type" class="task-group">
        <div class="group-header">
          <div class="group-title">
            <Icon :icon="getTaskTypeIcon(type)" class="group-icon" />
            <span>{{ getTaskTypeLabel(type) }}</span>
            <el-badge :value="typeTasks.length" class="group-badge" />
          </div>
          <div class="group-actions" v-if="allowControl">
            <el-button-group size="small">
              <el-button @click="pauseTasksOfType(type)" :disabled="!hasProcessingTasksOfType(type)">
                <Icon icon="mdi:pause" />
              </el-button>
              <el-button @click="retryTasksOfType(type)" :disabled="!hasFailedTasksOfType(type)">
                <Icon icon="mdi:refresh" />
              </el-button>
            </el-button-group>
          </div>
        </div>

        <div class="task-items">
          <TaskListItem v-for="task in typeTasks" :key="task.id" :task="task" :allow-control="allowControl"
            :compact="compact" @action="handleTaskAction" />
        </div>
      </div>
    </div>

    <div v-else class="flat-tasks">
      <TaskListItem v-for="task in tasks" :key="task.id" :task="task" :allow-control="allowControl" :compact="compact"
        @action="handleTaskAction" />
    </div>

    <div class="list-footer" v-if="tasks.length > 0">
      <div class="task-summary">
        <span class="summary-text">
          共 {{ tasks.length }} 个任务
          <template v-if="processingCount > 0">，{{ processingCount }} 个处理中</template>
          <template v-if="completedCount > 0">，{{ completedCount }} 个已完成</template>
          <template v-if="errorCount > 0">，{{ errorCount }} 个失败</template>
        </span>
      </div>

      <div class="list-actions" v-if="allowControl && !compact">
        <el-button size="small" @click="selectAll">
          <Icon icon="mdi:select-all" />
          全选
        </el-button>
        <el-button size="small" @click="selectNone" v-if="selectedTasks.length > 0">
          <Icon icon="mdi:select-off" />
          取消
        </el-button>
        <el-button size="small" type="danger" @click="batchAction('remove')" :disabled="selectedTasks.length === 0">
          <Icon icon="mdi:trash-can-outline" />
          批量删除
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'
import TaskListItem from './TaskListItem.vue'

interface Task {
  id: string
  type: string
  fileName: string
  status: string
  progress: number
  error?: string
  createdAt: number
  startTime?: number
  completedAt?: number
}

interface Props {
  tasks: Task[]
  allowControl?: boolean
  groupByType?: boolean
  compact?: boolean
  selectable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  allowControl: true,
  groupByType: false,
  compact: false,
  selectable: false
})

const emit = defineEmits<{
  'task-action': [action: string, taskId: string]
  'batch-action': [action: string, taskIds: string[]]
}>()

// 选中的任务
const selectedTasks = ref<string[]>([])

// 按类型分组的任务
const groupedTasks = computed(() => {
  const groups: Record<string, Task[]> = {}
  props.tasks.forEach(task => {
    if (!groups[task.type]) {
      groups[task.type] = []
    }
    groups[task.type].push(task)
  })
  return groups
})

// 统计信息
const processingCount = computed(() =>
  props.tasks.filter(t => t.status === 'processing').length
)

const completedCount = computed(() =>
  props.tasks.filter(t => t.status === 'completed').length
)

const errorCount = computed(() =>
  props.tasks.filter(t => t.status === 'error').length
)

// 类型检查方法
const hasProcessingTasksOfType = (type: string): boolean => {
  return groupedTasks.value[type]?.some(t => t.status === 'processing') || false
}

const hasFailedTasksOfType = (type: string): boolean => {
  return groupedTasks.value[type]?.some(t => t.status === 'error') || false
}

// 任务操作
const handleTaskAction = (action: string, taskId: string) => {
  emit('task-action', action, taskId)
}

const pauseTasksOfType = (type: string) => {
  const tasksToePause = groupedTasks.value[type]
    ?.filter(t => t.status === 'processing')
    .map(t => t.id) || []

  if (tasksToePause.length > 0) {
    emit('batch-action', 'pause', tasksToePause)
  }
}

const retryTasksOfType = (type: string) => {
  const tasksToRetry = groupedTasks.value[type]
    ?.filter(t => t.status === 'error')
    .map(t => t.id) || []

  if (tasksToRetry.length > 0) {
    emit('batch-action', 'retry', tasksToRetry)
  }
}

// 选择操作
const selectAll = () => {
  selectedTasks.value = props.tasks.map(t => t.id)
}

const selectNone = () => {
  selectedTasks.value = []
}

const batchAction = (action: string) => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要操作的任务')
    return
  }

  emit('batch-action', action, selectedTasks.value)
  selectedTasks.value = [] // 清空选择
}

// 工具方法
const getTaskTypeIcon = (type: string): string => {
  const icons = {
    'video-convert': 'mdi:video-outline',
    'audio-extract': 'mdi:music',
    'asr': 'mdi:microphone',
    'image-process': 'mdi:image-outline',
    'batch': 'mdi:playlist-check'
  }
  return icons[type as keyof typeof icons] || 'mdi:file'
}

const getTaskTypeLabel = (type: string): string => {
  const labels = {
    'video-convert': '视频转换',
    'audio-extract': '音频提取',
    'asr': '语音识别',
    'image-process': '图片处理',
    'batch': '批量处理'
  }
  return labels[type as keyof typeof labels] || type
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/index.scss' as *;

.global-task-list {
  .grouped-tasks {
    .task-group {
      // margin-bottom: $spacing-lg;
      border: 1px solid $border-light;
      border-radius: $border-radius;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: $spacing-small $spacing-base;
        background: $background-light;
        border-bottom: 1px solid $border-light;

        .group-title {
          display: flex;
          align-items: center;
          gap: $spacing-mini;
          font-weight: 500;
          color: $text-primary;

          .group-icon {
            font-size: 16px;
            color: $primary-color;
          }

          .group-badge {
            margin-left: $spacing-mini;
          }
        }
      }

      .task-items {
        padding: $spacing-small;
        background: white;
      }
    }
  }

  .flat-tasks {
    display: flex;
    flex-direction: column;
    gap: $spacing-small;
  }

  .list-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-base;
    padding: $spacing-small $spacing-base;
    background: $background-light;
    border: 1px solid $border-light;
    border-radius: $border-radius;

    .summary-text {
      font-size: 13px;
      color: $text-secondary;
    }

    .list-actions {
      display: flex;
      gap: $spacing-mini;
    }
  }
}

@media (max-width: 768px) {
  .global-task-list {
    .list-footer {
      flex-direction: column;
      gap: $spacing-small;
      align-items: stretch;

      .list-actions {
        justify-content: center;
      }
    }

    .grouped-tasks .task-group .group-header {
      flex-direction: column;
      gap: $spacing-small;
      align-items: flex-start;
    }
  }
}
</style>