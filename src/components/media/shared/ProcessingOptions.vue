<template>
  <div class="processing-options" :class="{ 'compact-mode': compact }">
    <!-- 视频转换选项 -->
    <div class="options-group" v-if="taskType === 'video-convert'">
      <!-- 注意：视频转换的具体选项已经在 VideoConverter.vue 组件中实现 -->
      <!-- 这里只保留基础的布局结构，避免重复 -->
    </div>

    <!-- 音频提取选项 -->
    <div class="options-group" v-if="taskType === 'audio-extract'">
      <!-- 注意：音频提取的具体选项已经在 AudioExtractor.vue 组件中实现 -->
      <!-- 这里只保留基础的布局结构，避免重复 -->
    </div>

    <!-- 语音识别选项 -->
    <div class="options-group" v-if="taskType === 'asr'">
      <el-row :gutter="16">
        <el-col :span="compact ? 24 : 12">
          <el-form-item label="识别语言">
            <el-select v-model="localOptions.language" placeholder="选择识别语言">
              <el-option label="中文" value="zh" />
              <el-option label="英文" value="en" />
              <el-option label="自动识别" value="auto" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="compact ? 24 : 12">
          <el-form-item label="输出格式">
            <el-checkbox-group v-model="localOptions.outputFormats" @change="handleOutputFormatsChange">
              <el-checkbox label="txt">文本文件</el-checkbox>
              <el-checkbox label="srt">SRT字幕</el-checkbox>
              <el-checkbox label="vtt">VTT字幕</el-checkbox>
              <el-checkbox label="json">JSON数据</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 高级设置 -->
      <div class="advanced-settings" v-if="showAdvanced && props.showAdvanced">
        <el-divider>高级设置</el-divider>

        <el-row :gutter="16">
          <el-col :span="compact ? 24 : 12">
            <el-form-item label="音频质量">
              <el-select v-model="localOptions.audioQuality" placeholder="选择音频质量">
                <el-option label="高质量" value="high" />
                <el-option label="标准质量" value="standard" />
                <el-option label="低质量" value="low" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="compact ? 24 : 12">
            <el-form-item label="其他选项">
              <div class="checkbox-group">
                <el-checkbox v-model="localOptions.enableTimestamps">包含时间戳</el-checkbox>
                <el-checkbox v-model="localOptions.filterSilence">过滤静音</el-checkbox>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 图片处理选项 -->
    <div class="options-group" v-if="taskType === 'image-process'">
      <el-row :gutter="16">
        <el-col :span="compact ? 24 : 8">
          <el-form-item label="输出格式">
            <el-select v-model="localOptions.outputFormat" placeholder="选择输出格式">
              <el-option label="JPEG" value="jpeg" />
              <el-option label="PNG" value="png" />
              <el-option label="WebP" value="webp" />
              <el-option label="AVIF" value="avif" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="compact ? 24 : 8">
          <el-form-item label="图片质量">
            <el-slider v-model="localOptions.quality" :min="10" :max="100" :step="5" show-input show-stops />
          </el-form-item>
        </el-col>

        <el-col :span="compact ? 24 : 8">
          <el-form-item label="尺寸设置">
            <div class="size-controls">
              <el-checkbox v-model="localOptions.resizeEnabled">调整尺寸</el-checkbox>
              <div v-if="localOptions.resizeEnabled" class="size-inputs">
                <el-input-number v-model="localOptions.width" :min="100" :max="4000" placeholder="宽度" size="small" />
                <span class="size-separator">×</span>
                <el-input-number v-model="localOptions.height" :min="100" :max="4000" placeholder="高度" size="small" />
                <el-checkbox v-model="localOptions.maintainAspectRatio" size="small">
                  保持比例
                </el-checkbox>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 图片优化选项 -->
      <div class="optimization-settings" v-if="showAdvanced && props.showAdvanced">
        <el-divider>优化设置</el-divider>

        <el-row :gutter="16">
          <el-col :span="compact ? 24 : 12">
            <el-form-item label="优化选项">
              <div class="checkbox-group">
                <el-checkbox v-model="localOptions.optimize">启用优化</el-checkbox>
                <el-checkbox v-model="localOptions.progressive">渐进式加载</el-checkbox>
                <el-checkbox v-model="localOptions.stripMetadata">移除元数据</el-checkbox>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="compact ? 24 : 12">
            <el-form-item label="色彩设置">
              <div class="color-controls">
                <el-select v-model="localOptions.colorSpace" placeholder="色彩空间" size="small">
                  <el-option label="sRGB" value="srgb" />
                  <el-option label="Adobe RGB" value="adobe-rgb" />
                  <el-option label="P3" value="p3" />
                </el-select>
                <el-slider v-model="localOptions.saturation" :min="0" :max="2" :step="0.1"
                  :format-tooltip="(val) => `饱和度: ${val}`" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 高级设置切换 -->
    <div class="advanced-toggle" v-if="hasAdvancedOptions && props.showAdvanced">
      <el-button type="text" size="small" @click="showAdvanced = !showAdvanced">
        <Icon :icon="showAdvanced ? 'mdi:chevron-up' : 'mdi:chevron-down'" />
        {{ showAdvanced ? '隐藏高级设置' : '显示高级设置' }}
      </el-button>
    </div>

    <!-- 预设配置 -->
    <div class="preset-controls" v-if="showPresets">
      <el-divider>预设配置</el-divider>

      <div class="preset-buttons">
        <el-button v-for="preset in availablePresets" :key="preset.name" size="small" @click="applyPreset(preset)">
          {{ preset.label }}
        </el-button>

        <el-button size="small" type="primary" @click="saveAsPreset" v-if="showSavePreset">
          保存预设
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'

// Props
interface Props {
  modelValue: any
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  compact?: boolean
  showPresets?: boolean
  showSavePreset?: boolean
  showAdvanced?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false,
  showPresets: false,
  showSavePreset: false,
  showAdvanced: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [options: any]
  'presetSaved': [preset: any]
}>()

// 响应式数据
const showAdvanced = ref(false)

// 默认选项配置
const defaultOptions = {
  'video-convert': {
    // 视频转换的选项在 VideoConverter.vue 中管理
  },
  'audio-extract': {
    // 音频提取的选项在 AudioExtractor.vue 中管理
  },
  'asr': {
    language: 'zh',
    outputFormats: ['txt', 'srt'],
    audioQuality: 'high',
    enableTimestamps: true,
    filterSilence: false
  },
  'image-process': {
    outputFormat: 'jpeg',
    quality: 80,
    resizeEnabled: false,
    width: 1920,
    height: 1080,
    maintainAspectRatio: true,
    optimize: true,
    progressive: false,
    stripMetadata: true,
    colorSpace: 'srgb',
    saturation: 1.0
  }
}

// 初始化本地选项
const localOptions = reactive({
  ...defaultOptions[props.taskType],
  ...props.modelValue
})

// 确保outputFormats是响应式数组（仅对ASR任务类型）
if (props.taskType === 'asr' && localOptions.outputFormats && !Array.isArray(localOptions.outputFormats)) {
  localOptions.outputFormats = [...(defaultOptions[props.taskType] as any).outputFormats]
}

// 计算属性
const hasAdvancedOptions = computed(() => {
  return ['asr', 'image-process'].includes(props.taskType)
})

const availablePresets = computed(() => {
  const presets = {
    'video-convert': [
      // 视频转换的预设在 VideoConverter.vue 中管理
    ],
    'audio-extract': [
      // 音频提取的预设在 AudioExtractor.vue 中管理
    ],
    'asr': [
      { name: 'chinese-subtitle', label: '中文字幕', options: { language: 'zh', outputFormats: ['srt', 'vtt'] } },
      { name: 'english-transcript', label: '英文转录', options: { language: 'en', outputFormats: ['txt', 'json'] } },
      { name: 'multi-format', label: '多格式输出', options: { language: 'auto', outputFormats: ['txt', 'srt', 'vtt', 'json'] } }
    ],
    'image-process': [
      { name: 'web-optimized', label: 'Web优化', options: { outputFormat: 'webp', quality: 85, optimize: true } },
      { name: 'high-quality', label: '高质量', options: { outputFormat: 'png', quality: 95, optimize: false } },
      { name: 'thumbnail', label: '缩略图', options: { outputFormat: 'jpeg', quality: 75, resizeEnabled: true, width: 400, height: 300 } }
    ]
  }

  return presets[props.taskType] || []
})

// 方法
const applyPreset = (preset: any) => {
  Object.assign(localOptions, preset.options)
  ElMessage.success(`已应用预设: ${preset.label}`)
}

const saveAsPreset = () => {
  // 这里可以实现保存预设的逻辑
  const presetName = `自定义预设 ${Date.now()}`
  const preset = {
    name: presetName.toLowerCase().replace(/\s+/g, '-'),
    label: presetName,
    options: { ...localOptions }
  }

  emit('presetSaved', preset)
  ElMessage.success('预设已保存')
}

// 处理输出格式变化
const handleOutputFormatsChange = (value: string[]) => {
  console.log(`[ProcessingOptions] 输出格式直接变化:`, value)
  localOptions.outputFormats = value
}

// 监听本地选项变化，同步到父组件
watch(localOptions, (newOptions) => {
  console.log(`[ProcessingOptions] 选项变化:`, newOptions)
  if (newOptions.outputFormats) {
    console.log(`[ProcessingOptions] 输出格式变化:`, newOptions.outputFormats)
  }
  emit('update:modelValue', { ...newOptions })
}, { deep: true })

// 监听外部传入的modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(localOptions, newValue)
  }
}, { deep: true })

// 监听任务类型变化，重置选项
watch(() => props.taskType, (newType) => {
  Object.assign(localOptions, defaultOptions[newType])
  showAdvanced.value = false
})
</script>

<style scoped lang="scss">
@use "sass:color";
@use '@/assets/styles/index.scss' as *;

.processing-options {
  &.compact-mode {
    .options-group {
      .el-row {
        .el-col {
          margin-bottom: $spacing-small;
        }
      }
    }

    :deep(.el-form-item) {
      margin-bottom: $spacing-small;
    }
  }

  .options-group {
    margin-bottom: $spacing-base;

    :deep(.el-form-item) {
      margin-bottom: $spacing-base;

      .el-form-item__label {
        color: $text-regular;
        font-weight: 500;
        font-size: 13px;
      }
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: $spacing-small;

      .el-checkbox {
        margin-bottom: 0;
      }
    }

    .size-controls {
      .size-inputs {
        display: flex;
        align-items: center;
        gap: $spacing-small;
        margin-top: $spacing-small;
        flex-wrap: wrap;

        .size-separator {
          color: $text-secondary;
          font-weight: bold;
        }

        .el-input-number {
          width: 80px;
        }
      }
    }

    .color-controls {
      display: flex;
      flex-direction: column;
      gap: $spacing-small;

      .el-select {
        width: 100%;
      }
    }
  }

  .advanced-settings,
  .optimization-settings {
    margin-top: $spacing-base;
    padding-top: $spacing-base;
  }

  .advanced-toggle {
    text-align: center;
    margin: $spacing-base 0;

    .el-button {
      color: $primary-color;

      &:hover {
        color: color.adjust($primary-color, $lightness: 10%);
      }
    }
  }

  .preset-controls {
    margin-top: $spacing-base;
    padding-top: $spacing-base;

    .preset-buttons {
      display: flex;
      gap: $spacing-small;
      flex-wrap: wrap;
      justify-content: center;

      .el-button {
        font-size: 12px;
      }
    }
  }
}

// 紧凑模式适配
@media (max-width: 768px) {
  .processing-options {
    .options-group {
      .el-row {
        .el-col {
          span: 24 !important;
        }
      }
    }

    .preset-controls {
      .preset-buttons {
        flex-direction: column;
        align-items: stretch;
      }
    }
  }
}
</style>