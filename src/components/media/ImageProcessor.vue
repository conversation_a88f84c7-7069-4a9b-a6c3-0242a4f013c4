<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="image-process"
    title="图片处理"
    icon="mdi:image-edit-outline"
    options-title="处理设置"
    :uploader-props="{
      maxFileSize: 50,
      maxFileCount: 50,
      uploadText: '拖拽图片文件到此处或点击上传',
      hint: '支持 JPG, PNG, WEBP, BMP, GIF 格式，单个文件最大 50MB'
    }"
    :default-options="{
      outputFormat: 'webp',
      quality: 85,
      resizeEnabled: false,
      maxWidth: 1920,
      maintainAspectRatio: true,
      optimize: true,
      stripMetadata: true
    }"
  >
    <template #extra-options>
      <!-- 图片处理的选项已在 ProcessingOptions 组件中实现 -->
      <!-- 这里可以添加 ProcessingOptions 中没有的特殊选项 -->
    </template>
    
    <template #content>
      <!-- 图片预览网格 -->
      <div v-if="fileList.length > 0" class="image-preview-grid">
        <el-divider>图片预览</el-divider>
        <div class="preview-container">
          <div 
            v-for="file in fileList.slice(0, 8)" 
            :key="file.uid"
            class="preview-item"
          >
            <el-image
              :src="file.url"
              :preview-src-list="[file.url]"
              fit="cover"
            />
            <div class="preview-info">
              {{ file.name }}
            </div>
          </div>
          <div v-if="fileList.length > 8" class="preview-more">
            +{{ fileList.length - 8 }} 更多
          </div>
        </div>
      </div>
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'
import { useMediaTasksStore } from '@/stores'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()
const tasksStore = useMediaTasksStore()

// 获取待处理文件列表
const fileList = computed(() => {
  return tasksStore.getPendingFiles('image-process')
})

defineExpose({
  processor
})
</script>

<style lang="scss" scoped>
.image-extra-options {
  margin-top: 16px;
  
  .unit {
    margin-left: 8px;
    color: #909399;
  }
}

.image-preview-grid {
  margin-top: 20px;
  
  .preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    
    .preview-item {
      position: relative;
      
      .el-image {
        width: 100%;
        height: 120px;
        border-radius: 4px;
        overflow: hidden;
      }
      
      .preview-info {
        margin-top: 4px;
        font-size: 12px;
        color: #606266;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    
    .preview-more {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 120px;
      background: #f5f7fa;
      border-radius: 4px;
      font-size: 14px;
      color: #909399;
    }
  }
}
</style>