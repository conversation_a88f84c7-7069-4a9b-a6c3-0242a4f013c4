<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="asr"
    title="语音识别"
    icon="mdi:microphone-outline"
    options-title="识别设置"
    :uploader-props="{
      maxFileSize: 500,
      uploadText: '拖拽音频/视频文件到此处或点击上传',
      hint: '支持音频：MP3, WAV, M4A, AAC, FLAC, OGG；视频：MP4, AVI, MOV 等，最大 500MB'
    }"
    :default-options="{
      language: 'zh',
      outputFormats: ['txt', 'srt'],
      enableTimestamps: true,
      filterSilence: true
    }"
  >
    <template #extra-options>
      <!-- ASR 的选项已在 ProcessingOptions 组件中实现 -->
      <!-- 这里可以添加 ProcessingOptions 中没有的特殊选项 -->
    </template>
    
    <template #content>
      <!-- ASR 结果预览 -->
      <div v-if="lastResult" class="asr-result-preview">
        <el-divider>识别结果预览</el-divider>
        <div class="result-text">
          {{ lastResult.text }}
        </div>
      </div>
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'
import { useMediaTasksStore } from '@/stores'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()
const tasksStore = useMediaTasksStore()

// 获取最后一个完成的任务结果
const lastResult = computed(() => {
  const results = Array.from(tasksStore.taskResults.values())
    .filter(r => r.taskType === 'asr' && r.success)
    .sort((a, b) => b.completedAt - a.completedAt)
  return results[0]?.data
})

defineExpose({
  processor
})
</script>

<style lang="scss" scoped>
.asr-extra-options {
  margin-top: 16px;
}

.asr-result-preview {
  margin-top: 20px;
  
  .result-text {
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: monospace;
    line-height: 1.6;
  }
}
</style>