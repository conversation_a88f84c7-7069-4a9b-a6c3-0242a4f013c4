<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="audio-extract"
    title="音频提取"
    icon="mdi:music-note-outline"
    options-title="提取设置"
    :uploader-props="{
      maxFileSize: 1024,
      uploadText: '拖拽视频文件到此处或点击上传',
      hint: '支持 MP4, AVI, MOV, MKV, FLV, WMV 格式，最大 1GB'
    }"
    :default-options="{
      outputFormat: 'mp3',
      quality: '192k',
      channels: 'stereo',
      normalization: false
    }"
  >
    <template #extra-options>
      <!-- 音频提取特有的选项 -->
      <div class="audio-extra-options">
        <el-form-item label="输出格式">
          <el-select v-model="outputFormat" placeholder="选择格式">
            <el-option label="MP3 (通用)" value="mp3" />
            <el-option label="WAV (无损)" value="wav" />
            <el-option label="AAC (高质量)" value="aac" />
            <el-option label="FLAC (无损压缩)" value="flac" />
            <el-option label="OGG (开源格式)" value="ogg" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="音频质量">
          <el-select v-model="quality" placeholder="选择质量">
            <el-option label="低 (64k)" value="64k" />
            <el-option label="中 (128k)" value="128k" />
            <el-option label="高 (192k)" value="192k" />
            <el-option label="极高 (256k)" value="256k" />
            <el-option label="最高 (320k)" value="320k" />
          </el-select>
        </el-form-item>

        <el-form-item label="声道选项">
          <el-radio-group v-model="channels">
            <el-radio label="stereo">立体声</el-radio>
            <el-radio label="mono">单声道</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="音频优化">
          <el-checkbox v-model="normalization">音量标准化</el-checkbox>
        </el-form-item>
      </div>
    </template>
    
    <template #content>
      <!-- 音频提取说明或简介 -->
      <div v-if="showHelp" class="audio-extract-help">
        <el-divider>功能说明</el-divider>
        <div class="help-content">
          <p>
            <i class="el-icon-info-filled" style="color: var(--el-color-primary);"></i>
            音频提取功能可以从视频文件中分离出音频轨道，支持多种输出格式和质量选项。
          </p>
          <ul>
            <li><strong>MP3</strong>：最常用的音频格式，兼容性最广</li>
            <li><strong>WAV</strong>：无损格式，保留原始音质，但文件较大</li>
            <li><strong>AAC</strong>：高效压缩格式，音质较好，适合移动设备</li>
            <li><strong>FLAC</strong>：无损压缩格式，保留音质同时减小文件大小</li>
            <li><strong>OGG</strong>：开源格式，较好的压缩率和音质</li>
          </ul>
        </div>
      </div>
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'
import { useMediaTasksStore } from '@/stores'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()
const tasksStore = useMediaTasksStore()

// 音频提取特有的选项
const outputFormat = ref('mp3')
const quality = ref('192k')
const channels = ref('stereo')
const normalization = ref(false)

// 是否显示帮助信息
const showHelp = ref(true)

// 文件列表状态
const fileList = computed(() => {
  return tasksStore.getPendingFiles('audio-extract')
})

// 同步选项到处理器
watch([outputFormat, quality, channels, normalization], () => {
  if (processor.value) {
    const options = processor.value.processingOptions
    options.outputFormat = outputFormat.value
    options.quality = quality.value
    // 使用索引签名设置额外的属性
    options['channels'] = channels.value
    options['normalization'] = normalization.value
  }
})

// 当有文件上传时隐藏帮助信息
watch(() => fileList.value.length, (newLength) => {
  showHelp.value = newLength === 0
})

// 暴露给父组件
defineExpose({
  processor
})
</script>

<style lang="scss" scoped>
.audio-extra-options {
  margin-top: 16px;
}

.audio-extract-help {
  margin-top: 20px;
  
  .help-content {
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
    
    p {
      margin-top: 0;
      margin-bottom: 12px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>