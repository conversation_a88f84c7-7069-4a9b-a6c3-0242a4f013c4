<template>
  <BaseMediaProcessor ref="processor" task-type="video-convert" title="视频转换" icon="mdi:video-outline"
    options-title="转换设置" :uploader-props="{
      maxFileSize: 1024,
      uploadText: '拖拽视频文件到此处或点击上传',
      hint: '支持 MP4, AVI, MOV, MKV, FLV, WMV 格式，最大 1GB'
    }" :default-options="{
      outputFormat: 'mp4',
      videoQuality: 'high',
      videoBitrate: '2500k',
      audioBitrate: '192k',
      resolution: '1080p',
      fps: 30,
      codec: 'h264',
      resizeEnabled: false,
      maintainAspectRatio: true
    }">
    <template #extra-options>
      <!-- 快速预设 -->
      <div v-if="showPresets" class="video-presets">
        <div class="presets-grid">
          <div class="preset-card" :class="{ active: activePreset === preset.id }" v-for="preset in presets"
            :key="preset.id" @click="applyPreset(preset)">
            <Icon :icon="preset.icon" class="preset-icon" />
            <div class="preset-name">{{ preset.name }}</div>
            <div class="preset-desc">{{ preset.description }}</div>
          </div>
        </div>
      </div>

      <!-- 转换设置 -->
      <div class="video-extra-options">
        <el-form-item label="输出格式">
          <el-select v-model="outputFormat" placeholder="选择格式">
            <el-option label="MP4 (通用)" value="mp4" />
            <el-option label="AVI (兼容性好)" value="avi" />
            <el-option label="MOV (Apple)" value="mov" />
            <el-option label="MKV (高质量)" value="mkv" />
            <el-option label="WEBM (Web)" value="webm" />
            <el-option label="FLV (Flash)" value="flv" />
          </el-select>
        </el-form-item>

        <el-form-item label="视频质量">
          <el-select v-model="videoQuality" placeholder="选择质量">
            <el-option label="低质量 (快速)" value="low" />
            <el-option label="中等质量" value="medium" />
            <el-option label="高质量" value="high" />
            <el-option label="最高质量" value="highest" />
          </el-select>
        </el-form-item>

        <el-form-item label="分辨率">
          <el-select v-model="resolution" placeholder="选择分辨率">
            <el-option label="保持原始" value="original" />
            <el-option label="360p (SD)" value="360p" />
            <el-option label="480p (SD)" value="480p" />
            <el-option label="720p (HD)" value="720p" />
            <el-option label="1080p (Full HD)" value="1080p" />
            <el-option label="2K (2560x1440)" value="2k" />
            <el-option label="4K (3840x2160)" value="4k" />
          </el-select>
        </el-form-item>

        <el-form-item label="帧率">
          <el-select v-model="fps" placeholder="选择帧率">
            <el-option label="保持原始" :value="-1" />
            <el-option label="24 fps (电影)" :value="24" />
            <el-option label="30 fps (标准)" :value="30" />
            <el-option label="60 fps (流畅)" :value="60" />
          </el-select>
        </el-form-item>

        <el-form-item label="视频编码">
          <el-select v-model="codec" placeholder="选择编码">
            <el-option label="H.264 (兼容性最好)" value="h264" />
            <el-option label="H.265/HEVC (高压缩)" value="h265" />
            <el-option label="VP9 (Web优化)" value="vp9" />
          </el-select>
        </el-form-item>

        <el-form-item label="其他选项">
          <el-checkbox v-model="removeAudio">移除音频</el-checkbox>
          <el-checkbox v-model="fastStart">快速启动优化</el-checkbox>
        </el-form-item>
      </div>
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Icon } from '@iconify/vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'
import { useMediaTasksStore } from '@/stores'
import { ElMessage } from 'element-plus'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()
const tasksStore = useMediaTasksStore()

// 视频转换特有的选项
const outputFormat = ref('mp4')
const videoQuality = ref('high')
const resolution = ref('1080p')
const fps = ref(30)
const codec = ref('h264')
const removeAudio = ref(false)
const fastStart = ref(true)

// 预设始终显示
const showPresets = ref(true)

// 当前选中的预设 - 从store获取持久化状态
const activePreset = computed({
  get: () => tasksStore.getActivePreset('video-convert'),
  set: (value: string | null) => tasksStore.setActivePreset('video-convert', value)
})

// 视频转换预设
const presets = ref([
  {
    id: 'web',
    name: 'Web 优化',
    icon: 'mdi:web',
    description: 'MP4, H.264, 1080p',
    settings: {
      outputFormat: 'mp4',
      videoQuality: 'medium',
      resolution: '1080p',
      codec: 'h264',
      fastStart: true
    }
  },
  {
    id: 'mobile',
    name: '移动设备',
    icon: 'mdi:cellphone',
    description: 'MP4, H.264, 720p',
    settings: {
      outputFormat: 'mp4',
      videoQuality: 'medium',
      resolution: '720p',
      codec: 'h264',
      fastStart: true
    }
  },
  {
    id: 'hq',
    name: '高质量存档',
    icon: 'mdi:quality-high',
    description: 'MKV, H.265, 原始分辨率',
    settings: {
      outputFormat: 'mkv',
      videoQuality: 'highest',
      resolution: 'original',
      codec: 'h265',
      fastStart: false
    }
  },
  {
    id: 'compress',
    name: '压缩文件',
    icon: 'mdi:folder-zip',
    description: 'MP4, H.265, 720p',
    settings: {
      outputFormat: 'mp4',
      videoQuality: 'low',
      resolution: '720p',
      codec: 'h265',
      fastStart: true
    }
  }
])

// 应用预设
const applyPreset = (preset: any) => {
  outputFormat.value = preset.settings.outputFormat
  videoQuality.value = preset.settings.videoQuality
  resolution.value = preset.settings.resolution
  codec.value = preset.settings.codec
  fastStart.value = preset.settings.fastStart

  // 设置当前选中的预设
  activePreset.value = preset.id

  ElMessage.success(`已应用预设：${preset.name}`)
}

// 同步选项到处理器
watch([outputFormat, videoQuality, resolution, fps, codec, removeAudio, fastStart], () => {
  if (processor.value) {
    const options = processor.value.processingOptions
    options.outputFormat = outputFormat.value
    options.videoQuality = videoQuality.value
    options.resolution = resolution.value
    options.fps = fps.value
    options.codec = codec.value
    options.removeAudio = removeAudio.value
    options.fastStart = fastStart.value
  }
})

// 监听手动修改选项，清除预设选中状态
watch([outputFormat, videoQuality, resolution, fps, codec, removeAudio, fastStart], () => {
  // 检查当前设置是否与任何预设匹配
  const currentSettings = {
    outputFormat: outputFormat.value,
    videoQuality: videoQuality.value,
    resolution: resolution.value,
    codec: codec.value,
    fastStart: fastStart.value
  }

  const matchingPreset = presets.value.find(preset => {
    const settings = preset.settings
    return settings.outputFormat === currentSettings.outputFormat &&
      settings.videoQuality === currentSettings.videoQuality &&
      settings.resolution === currentSettings.resolution &&
      settings.codec === currentSettings.codec &&
      settings.fastStart === currentSettings.fastStart
  })

  activePreset.value = matchingPreset ? matchingPreset.id : null
}, { deep: true })

// 组件挂载时检查默认设置是否匹配预设
onMounted(() => {
  // 如果store中没有保存的预设，检查默认设置是否匹配某个预设
  if (!activePreset.value) {
    const defaultSettings = {
      outputFormat: outputFormat.value,
      videoQuality: videoQuality.value,
      resolution: resolution.value,
      codec: codec.value,
      fastStart: fastStart.value
    }

    const matchingPreset = presets.value.find(preset => {
      const settings = preset.settings
      return settings.outputFormat === defaultSettings.outputFormat &&
        settings.videoQuality === defaultSettings.videoQuality &&
        settings.resolution === defaultSettings.resolution &&
        settings.codec === defaultSettings.codec &&
        settings.fastStart === defaultSettings.fastStart
    })

    if (matchingPreset) {
      activePreset.value = matchingPreset.id
    }
  }
})

// 暴露给父组件
defineExpose({
  processor
})
</script>

<style lang="scss" scoped>
.video-presets {
  margin-bottom: 20px;

  .presets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;

    .preset-card {
      padding: 20px;
      background: #f5f7fa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      text-align: center;
      border: 2px solid transparent;

      &:hover {
        background: #e6e9ed;
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.active {
        background: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary);

        .preset-icon {
          color: var(--el-color-primary);
        }

        .preset-name {
          color: var(--el-color-primary);
        }
      }

      .preset-icon {
        font-size: 32px;
        color: var(--el-color-primary);
        margin-bottom: 8px;
      }

      .preset-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .preset-desc {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>